import React from 'react';
import { AttributeCollection, AttributeField } from '../../types/gamestate';
import { Tabs } from '../ui/Tabs';
import { Progress } from '../ui/Progress';
import { StarRating } from '../ui/StarRating';
import { Tag } from '../ui/Tag';
import { Tooltip } from '../ui/Tooltip';
import { ImagePreview } from '../ui/ImagePreview';
import { Sparkles, Users, Heart, Zap, Shield, Sword, TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';
import {
  TextAttributeCard,
  NumberAttributeCard,
  StarRatingCard,
  ProgressBarCard
} from './attribute-cards';
import {
  getAttributeGroupTheme,
  getAttributeFieldIcon,
  getAttributeGroupIcon,
  getAttributeChangeType,
  hasAttributeCollectionChanges,
  getAttributeCollectionChangeType,
  getAttributeDisplayConfig,
  groupAttributesByType
} from '../../utils/attributeHelpers';
import '../../styles/attribute-themes.css';

interface AttributeCollectionTabsProps {
  attributeCollections: AttributeCollection[];
}

const AttributeFieldComponent: React.FC<{ field: AttributeField; theme?: 'red' | 'blue' | 'purple' | 'green' | 'default' }> = ({ field, theme = 'default' }) => {
  const icon = getAttributeFieldIcon(field.id);
  const config = getAttributeDisplayConfig(field);





  const renderField = () => {
    switch (field.type) {
      case 'progress':
        return (
          <ProgressBarCard
            field={field}
            icon={icon}
            theme={theme}
            maxValue={config.maxValue}
          />
        );

      case 'starrating':
        return (
          <StarRatingCard
            field={field}
            icon={icon}
            theme={theme}
            maxStars={config.maxStars}
          />
        );
      
      case 'tags':
        return (
          <div className="space-y-2">
            <div className="flex items-center space-x-2 min-w-0">
              {icon}
              <span className="text-sm font-medium text-gray-200 truncate">{field.label}</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {Array.isArray(field.value) ? field.value.map((tag: any, index: number) => (
                <Tag
                  key={index}
                  variant="default"
                  size="sm"
                  className={`${
                    theme === 'red' ? 'bg-red-500/20 text-red-300 border-red-500/50' :
                    theme === 'blue' ? 'bg-blue-500/20 text-blue-300 border-blue-500/50' :
                    theme === 'purple' ? 'bg-purple-500/20 text-purple-300 border-purple-500/50' :
                    theme === 'green' ? 'bg-green-500/20 text-green-300 border-green-500/50' :
                    'bg-purple-500/20 text-purple-300 border-purple-500/50'
                  } hover:opacity-80 transition-colors`}
                >
                  {typeof tag === 'object' && tag.text ? tag.text : String(tag)}
                </Tag>
              )) : (
                <Tag
                  variant="default"
                  size="sm"
                  className={`${
                    theme === 'red' ? 'bg-red-500/20 text-red-300 border-red-500/50' :
                    theme === 'blue' ? 'bg-blue-500/20 text-blue-300 border-blue-500/50' :
                    theme === 'purple' ? 'bg-purple-500/20 text-purple-300 border-purple-500/50' :
                    theme === 'green' ? 'bg-green-500/20 text-green-300 border-green-500/50' :
                    'bg-purple-500/20 text-purple-300 border-purple-500/50'
                  }`}
                >
                  {typeof field.value === 'object' && field.value.text ? field.value.text : String(field.value)}
                </Tag>
              )}
            </div>
          </div>
        );

      case 'relationship':
        return (
          <ProgressBarCard
            field={field}
            icon={<Heart className="w-4 h-4" />}
            theme={theme}
            maxValue={100}
          />
        );

      case 'textarea':
        return (
          <TextAttributeCard
            field={field}
            icon={icon}
            theme={theme}
          />
        );

      case 'image_prompt':
        const visualPrompt = field.value as any;
        return (
          <div className="space-y-2">
            <div className="flex items-center space-x-2 min-w-0">
              <Sparkles className="w-4 h-4 text-purple-400 flex-shrink-0" />
              <span className="text-sm font-medium text-gray-200 truncate">{field.label}</span>
            </div>
            <div className="relative">
              {visualPrompt?.imageUrl ? (
                <div className="aspect-video rounded-lg overflow-hidden">
                  <ImagePreview
                    src={visualPrompt.imageUrl}
                    alt={visualPrompt.description || field.label}
                    className="w-full h-full"
                    showGenerateButton={true}
                    onGenerateClick={() => console.log('重新生成图片')}
                  />
                </div>
              ) : (
                <div className="aspect-video bg-gray-800 border-2 border-dashed border-gray-600 rounded-lg flex flex-col items-center justify-center text-gray-400 hover:border-pink-500/50 transition-colors">
                  <Sparkles className="w-8 h-8 mb-2" />
                  <button className="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg text-sm font-medium transition-colors">
                    生成图片
                  </button>
                </div>
              )}
              {visualPrompt?.description && (
                <p className="text-xs text-gray-400 mt-2 leading-relaxed prevent-overflow">
                  {visualPrompt.description}
                </p>
              )}
            </div>
          </div>
        );
      
      case 'number':
        return (
          <NumberAttributeCard
            field={field}
            icon={icon}
            theme={theme}
            showProgress={config.showProgress}
            maxValue={config.maxValue}
          />
        );

      case 'text':
      default:
        return (
          <TextAttributeCard
            field={field}
            icon={icon}
            theme={theme}
          />
        );
    }
  };

  return renderField();
};

export const AttributeCollectionTabs: React.FC<AttributeCollectionTabsProps> = ({
  attributeCollections
}) => {
  if (!attributeCollections || attributeCollections.length === 0) {
    return (
      <div className="text-center py-8 text-gray-400">
        <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p className="text-lg mb-2">暂无属性信息</p>
        <p className="text-sm">角色属性将在互动中动态更新</p>
      </div>
    );
  }

  const tabItems = attributeCollections.map(collection => {
    const hasChanges = hasAttributeCollectionChanges(collection);
    const changeType = getAttributeCollectionChangeType(collection);
    const theme = getAttributeGroupTheme(collection.id);
    const groupIcon = getAttributeGroupIcon(collection.id);

    // 按类型分组属性
    const groupedFields = groupAttributesByType(collection.fields);

    return {
      id: collection.id,
      label: collection.label || (collection as any).name || collection.id,
      theme: theme,
      icon: hasChanges ? (
        <div className="relative">
          {changeType === 'increase' && <TrendingUp className="w-4 h-4 text-red-400" />}
          {changeType === 'decrease' && <TrendingDown className="w-4 h-4 text-green-400" />}
          {changeType === 'new' && <Sparkles className="w-4 h-4 text-blue-400" />}
          {changeType === 'mixed' && <AlertCircle className="w-4 h-4 text-yellow-400" />}

          {/* 变化指示点 */}
          <div className={`absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse ${
            changeType === 'increase' ? 'bg-red-400' :
            changeType === 'decrease' ? 'bg-green-400' :
            changeType === 'new' ? 'bg-blue-400' :
            'bg-yellow-400'
          }`} />
        </div>
      ) : groupIcon,
      badge: hasChanges ? (
        <div className="attribute-badge">
          {collection.fields.filter(field => getAttributeChangeType(field.reason) !== null).length}
        </div>
      ) : undefined,
      content: (
        <div className="attribute-container space-y-6">
          {/* 数值属性 */}
          {groupedFields.number.length > 0 && (
            <div className="w-full">
              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center space-x-2">
                <Zap className="w-4 h-4" />
                <span>数值属性</span>
              </h4>
              <div className="attribute-grid">
                {groupedFields.number.map(field => (
                  <AttributeFieldComponent key={field.id} field={field} theme={theme} />
                ))}
              </div>
            </div>
          )}

          {/* 星级属性 */}
          {groupedFields.starrating.length > 0 && (
            <div className="w-full">
              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center space-x-2">
                <Sparkles className="w-4 h-4" />
                <span>星级评分</span>
              </h4>
              <div className="attribute-grid">
                {groupedFields.starrating.map(field => (
                  <AttributeFieldComponent key={field.id} field={field} theme={theme} />
                ))}
              </div>
            </div>
          )}

          {/* 进度条属性 */}
          {groupedFields.progress.length > 0 && (
            <div className="w-full">
              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center space-x-2">
                <Shield className="w-4 h-4" />
                <span>进度属性</span>
              </h4>
              <div className="attribute-grid">
                {groupedFields.progress.map(field => (
                  <AttributeFieldComponent key={field.id} field={field} theme={theme} />
                ))}
              </div>
            </div>
          )}

          {/* 文本属性 */}
          {groupedFields.text.length > 0 && (
            <div className="w-full">
              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>基础信息</span>
              </h4>
              <div className="attribute-grid">
                {groupedFields.text.map(field => (
                  <AttributeFieldComponent key={field.id} field={field} theme={theme} />
                ))}
              </div>
            </div>
          )}

          {/* 标签属性 */}
          {groupedFields.tags.length > 0 && (
            <div className="w-full">
              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center space-x-2">
                <Heart className="w-4 h-4" />
                <span>标签属性</span>
              </h4>
              <div className="space-y-3 w-full">
                {groupedFields.tags.map(field => (
                  <AttributeFieldComponent key={field.id} field={field} theme={theme} />
                ))}
              </div>
            </div>
          )}

          {/* 其他属性 */}
          {groupedFields.other.length > 0 && (
            <div className="w-full">
              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center space-x-2">
                <AlertCircle className="w-4 h-4" />
                <span>其他属性</span>
              </h4>
              <div className="space-y-3 w-full">
                {groupedFields.other.map(field => (
                  <AttributeFieldComponent key={field.id} field={field} theme={theme} />
                ))}
              </div>
            </div>
          )}
        </div>
      )
    };
  });

  return <Tabs items={tabItems} variant="underline" size="md" />;
};