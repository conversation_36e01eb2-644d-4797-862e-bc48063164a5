/**
 * AI服务相关的类型定义
 * 定义AI智能体的输入输出格式和配置
 */

import { z } from 'zod';

// ===== AI智能体配置 =====

export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  model: string;
  maxRetries: number;
  timeoutMs: number;
  temperature: number;
  maxTokens: number;
}

export interface AIServiceConfig {
  maxRetries: number;
  timeoutMs: number;
  fallbackEnabled: boolean;
  retryDelayMs: number;
}

// ===== AI调用结果类型 =====

export interface AICallResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  retryCount: number;
  executionTime: number;
}

export interface AgentResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
  executionTime?: number;
}

// ===== Agent A (创造者) 类型 =====

export interface AgentAInput {
  currentState: any; // GameSaveState
  userInput: string;
  sectionId?: string;
  loreMarkdown?: string;
}

export interface AgentAOutput {
  mode: 'create' | 'chat';
  narrative?: string;
  suggestedOptions: string[];
  sectionTitle?: string;
  sectionsId?: string;
  answer: string;
  chaptersInfo?: {
    id: string;
    chapterTitle: string;
    time: string;
    location: string;
    environment: string;
  };
}

// ===== Agent B (演员) 类型 =====

export interface AgentBInput {
  currentState: any; // GameSaveState
  prompt: string;
  characterId: string;
}

export interface AgentBOutput {
  response: string;
  characterId: string;
  timestamp: string;
}

// ===== Agent C (分析师) 类型 =====

export interface AgentCInput {
  currentState: any; // GameSaveState
  agentAOutput?: AgentAOutput;
  agentBOutput?: AgentBOutput;
  userInput: string;
}

export interface AgentCOutput {
  updatedGameState: any; // GameSaveState
  formattedUIContent: any[]; // FormattedDialogueMessage[]
  suggestedOptions: string[];
  sectionTitle?: string;
  answer?: string;
}

// ===== Zod验证Schema =====

export const AgentAOutputSchema = z.object({
  mode: z.enum(['create', 'chat']),
  narrative: z.string().optional(),
  suggestedOptions: z.array(z.string()),
  sectionTitle: z.string().optional(),
  sectionsId: z.string().optional(),
  answer: z.string(),
  chaptersInfo: z.object({
    id: z.string(),
    chapterTitle: z.string(),
    time: z.string(),
    location: z.string(),
    environment: z.string()
  }).optional()
});

export const AgentBOutputSchema = z.object({
  response: z.string(),
  characterId: z.string(),
  timestamp: z.string()
});

export const AgentCOutputSchema = z.object({
  updatedGameState: z.any(), // 复杂对象，暂时使用any
  formattedUIContent: z.array(z.object({
    id: z.string(),
    sender: z.enum(['user', 'ai', 'system']),
    timestamp: z.string(),
    characterId: z.string().optional(),
    isNarrative: z.boolean().optional(),
    audioUrl: z.string().optional(),
    thinkingContent: z.string().optional(),
    options: z.array(z.string()).optional(),
    sectionTitle: z.string().optional(),
    content: z.object({
      ask: z.string().optional(),
      answer: z.string().optional(),
      narrative: z.string().optional()
    }).optional()
  })),
  suggestedOptions: z.array(z.string()),
  sectionTitle: z.string().optional(),
  answer: z.string().optional()
});

// ===== AI调用参数类型 =====

export interface ChatCompletionParams {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  max_tokens?: number;
  response_format?: {
    type: 'text' | 'json_object';
  };
}

export interface ChatCompletionResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
  }>;
}

// ===== 智能体链式调用类型 =====

export interface AgentContext {
  gameId: string;
  sessionId: string;
  userInput: string;
  currentState: any;
  characterAttributeSchema?: any; // 角色属性架构定义
  metadata?: Record<string, any>;
}

export interface ChainStep {
  agent: any; // BaseAgent
  inputMapper?: (input: any, context: AgentContext) => any;
  outputValidator?: (output: any) => boolean;
  errorHandler?: (error: Error, context: AgentContext) => any;
}

export interface ChainResult {
  success: boolean;
  results: AgentResult[];
  error?: string;
  totalExecutionTime: number;
}

// ===== 错误类型 =====

export interface AIError extends Error {
  type: 'timeout' | 'rate_limit' | 'invalid_response' | 'network' | 'unknown';
  retryable: boolean;
  retryCount?: number;
}

// ===== 提示词模板类型 =====

export interface PromptTemplate {
  system: string;
  user: string;
  variables?: Record<string, string>;
}

export interface PromptBuilder {
  buildAgentAPrompt(currentState: any, userInput: string): PromptTemplate;
  buildAgentBPrompt(currentState: any, userInput: AgentBInput): PromptTemplate;
  buildAgentCPrompt(currentState: any, agentAOutput: AgentAOutput, agentBOutput?: AgentBOutput): PromptTemplate;
}
