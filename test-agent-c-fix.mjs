#!/usr/bin/env node

/**
 * 测试 Agent C 属性DNA同步修复效果
 * 验证新创建的角色属性是否完全符合 meta 蓝图的定义
 */

import fs from 'fs/promises';
import path from 'path';

const GAME_DATA_DIR = './gamedata';

async function testAgentCFix() {
  console.log('🧪 开始测试 Agent C 属性DNA同步修复效果...\n');

  try {
    // 1. 查找所有游戏目录
    const gameDirectories = await fs.readdir(GAME_DATA_DIR);
    const gameIds = gameDirectories.filter(dir => dir.startsWith('game_'));

    if (gameIds.length === 0) {
      console.log('❌ 未找到任何游戏数据目录');
      return;
    }

    console.log(`📁 找到 ${gameIds.length} 个游戏目录`);

    for (const gameId of gameIds) {
      await testGameAttributeConsistency(gameId);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

async function testGameAttributeConsistency(gameId) {
  console.log(`\n🎮 测试游戏: ${gameId}`);
  
  try {
    // 读取 game_meta.json
    const metaPath = path.join(GAME_DATA_DIR, gameId, 'game_meta.json');
    const metaContent = await fs.readFile(metaPath, 'utf-8');
    const gameMeta = JSON.parse(metaContent);

    if (!gameMeta.characterAttributeSchema) {
      console.log('⚠️  该游戏没有 characterAttributeSchema 定义');
      return;
    }

    const schema = gameMeta.characterAttributeSchema;
    console.log(`📋 Meta蓝图定义了 ${schema.groups?.length || 0} 个属性组`);

    // 显示属性组详情
    if (schema.groups) {
      schema.groups.forEach((group, index) => {
        console.log(`   ${index + 1}. ${group.name} (${group.fields?.length || 0} 个字段)`);
        if (group.fields) {
          group.fields.forEach((field, fieldIndex) => {
            const rules = field.rules ? ` [规则: ${field.rules.validationRules || '无'}]` : '';
            console.log(`      - ${field.name} (${field.displayType})${rules}`);
          });
        }
      });
    }

    // 查找存档文件
    const savesDir = path.join(GAME_DATA_DIR, gameId, 'saves');
    try {
      const saveFiles = await fs.readdir(savesDir);
      const sessionFiles = saveFiles.filter(file => file.startsWith('session_') && file.endsWith('.json'));
      
      if (sessionFiles.length === 0) {
        console.log('📝 该游戏暂无存档文件');
        return;
      }

      console.log(`💾 找到 ${sessionFiles.length} 个存档文件`);

      // 测试每个存档的角色属性
      for (const sessionFile of sessionFiles) {
        await testSessionCharacterAttributes(gameId, sessionFile, schema);
      }

    } catch (error) {
      console.log('📝 该游戏暂无存档目录或存档文件');
    }

  } catch (error) {
    console.error(`❌ 测试游戏 ${gameId} 时发生错误:`, error.message);
  }
}

async function testSessionCharacterAttributes(gameId, sessionFile, schema) {
  const sessionId = sessionFile.replace('session_', '').replace('.json', '');
  console.log(`\n  📄 测试存档: ${sessionId}`);

  try {
    // 读取存档文件
    const sessionPath = path.join(GAME_DATA_DIR, gameId, 'saves', sessionFile);
    const sessionContent = await fs.readFile(sessionPath, 'utf-8');
    const sessionData = JSON.parse(sessionContent);

    // 读取角色数据
    const charactersPath = path.join(GAME_DATA_DIR, gameId, 'saves', `characters_${sessionId}.json`);
    let charactersData;
    try {
      const charactersContent = await fs.readFile(charactersPath, 'utf-8');
      charactersData = JSON.parse(charactersContent);
    } catch (error) {
      console.log('    ⚠️  未找到独立的角色数据文件，使用存档中的角色数据');
      charactersData = { characters: sessionData.characters || [] };
    }

    const characters = charactersData.characters || [];
    console.log(`    👥 存档中有 ${characters.length} 个角色`);

    if (characters.length === 0) {
      console.log('    📝 该存档暂无角色数据');
      return;
    }

    // 分析每个角色的属性一致性
    let consistentCount = 0;
    let inconsistentCount = 0;

    for (const character of characters) {
      const isConsistent = checkCharacterAttributeConsistency(character, schema);
      if (isConsistent) {
        consistentCount++;
        console.log(`    ✅ ${character.name || character.id}: 属性结构一致`);
      } else {
        inconsistentCount++;
        console.log(`    ❌ ${character.name || character.id}: 属性结构不一致`);
        
        // 显示详细的不一致信息
        analyzeAttributeInconsistency(character, schema);
      }
    }

    // 总结
    const totalCharacters = characters.length;
    const consistencyRate = ((consistentCount / totalCharacters) * 100).toFixed(1);
    console.log(`    📊 一致性统计: ${consistentCount}/${totalCharacters} (${consistencyRate}%)`);

    if (inconsistentCount > 0) {
      console.log(`    ⚠️  发现 ${inconsistentCount} 个角色的属性结构与meta蓝图不一致`);
    }

  } catch (error) {
    console.error(`    ❌ 测试存档 ${sessionId} 时发生错误:`, error.message);
  }
}

function checkCharacterAttributeConsistency(character, schema) {
  if (!character.attributeCollections || !schema.groups) {
    return false;
  }

  // 检查属性组数量
  if (character.attributeCollections.length !== schema.groups.length) {
    return false;
  }

  // 检查每个属性组
  for (let i = 0; i < schema.groups.length; i++) {
    const schemaGroup = schema.groups[i];
    const charGroup = character.attributeCollections.find(g => g.id === schemaGroup.id);

    if (!charGroup) {
      return false;
    }

    // 检查字段数量
    if (!charGroup.fields || !schemaGroup.fields) {
      continue;
    }

    if (charGroup.fields.length !== schemaGroup.fields.length) {
      return false;
    }

    // 检查每个字段
    for (const schemaField of schemaGroup.fields) {
      const charField = charGroup.fields.find(f => f.id === schemaField.id);
      if (!charField) {
        return false;
      }

      // 检查字段类型
      if (charField.type !== schemaField.displayType) {
        return false;
      }
    }
  }

  return true;
}

function analyzeAttributeInconsistency(character, schema) {
  console.log(`      🔍 详细分析 ${character.name || character.id}:`);
  
  if (!character.attributeCollections) {
    console.log(`        - 缺少 attributeCollections`);
    return;
  }

  if (!schema.groups) {
    console.log(`        - Schema 缺少 groups 定义`);
    return;
  }

  console.log(`        - 角色属性组数量: ${character.attributeCollections.length}`);
  console.log(`        - Schema属性组数量: ${schema.groups.length}`);

  // 检查缺失的属性组
  for (const schemaGroup of schema.groups) {
    const charGroup = character.attributeCollections.find(g => g.id === schemaGroup.id);
    if (!charGroup) {
      console.log(`        - 缺失属性组: ${schemaGroup.name} (${schemaGroup.id})`);
    } else {
      // 检查字段
      const schemaFieldCount = schemaGroup.fields?.length || 0;
      const charFieldCount = charGroup.fields?.length || 0;
      if (schemaFieldCount !== charFieldCount) {
        console.log(`        - 属性组 ${schemaGroup.name} 字段数量不匹配: ${charFieldCount}/${schemaFieldCount}`);
      }
    }
  }

  // 检查多余的属性组
  for (const charGroup of character.attributeCollections) {
    const schemaGroup = schema.groups.find(g => g.id === charGroup.id);
    if (!schemaGroup) {
      console.log(`        - 多余属性组: ${charGroup.label || charGroup.id}`);
    }
  }
}

// 运行测试
testAgentCFix().then(() => {
  console.log('\n✅ 测试完成');
}).catch(error => {
  console.error('\n❌ 测试失败:', error);
  process.exit(1);
});
