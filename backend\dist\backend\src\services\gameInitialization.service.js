/**
 * 游戏初始化服务
 * 负责协调整个游戏初始化流程，从game_meta读取到Agent调用再到状态保存
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import { promises as fs } from 'fs';
import path from 'path';
import { AgentManager } from './ai/AgentManager.js';
import * as fileSystemService from './fileSystem.service.js';
import * as db from './db.service.js';
import { isErrorResult } from '../../../types/errors.js';
/**
 * 根据字段类型和规则获取默认值
 */
function getDefaultValueForField(field) {
    const displayType = field.displayType;
    const rules = field.rules;
    switch (displayType) {
        case 'number':
            if (rules && rules.range && rules.range.default !== undefined) {
                return rules.range.default;
            }
            return 0;
        case 'progress':
            if (rules && rules.range && rules.range.default !== undefined) {
                return rules.range.default;
            }
            return 50; // 默认50%
        case 'starrating':
            if (rules && rules.range && rules.range.default !== undefined) {
                return rules.range.default;
            }
            return 3; // 默认3星
        case 'tags':
            return [];
        case 'text':
        default:
            return '待设定';
    }
}
/**
 * 将角色的 attributes 转换为 attributeCollections 格式
 */
function convertAttributesToCollections(character, characterAttributeSchema) {
    if (!characterAttributeSchema || !characterAttributeSchema.groups) {
        console.warn('[GameInitialization] characterAttributeSchema 不可用，无法转换属性');
        return [];
    }
    console.log(`[GameInitialization] 为角色 ${character.name} 转换属性格式`);
    return characterAttributeSchema.groups.map((group) => ({
        id: group.id,
        label: group.name,
        fields: (group.fields || []).map((field) => {
            // 尝试从角色的现有属性中获取值
            let value = getDefaultValueForField(field);
            let reason = '基于角色设定的初始值';
            // 如果角色有 attributes，尝试迁移
            if (character.attributes && character.attributes[field.id] !== undefined) {
                value = character.attributes[field.id];
                reason = '从角色蓝图迁移';
            }
            return {
                id: field.id,
                label: field.name,
                type: field.displayType,
                value: value,
                reason: reason
            };
        })
    }));
}
export class GameInitializationService {
    constructor() {
        this.maxRetries = 3;
        this.agentManager = new AgentManager();
    }
    /**
     * 将AgentCOutput转换为GameSaveState
     */
    convertToGameSaveState(agentCResult, originalGameState) {
        var _a, _b;
        // 合并 Agent C 的更新到原始游戏状态
        const updatedGameState = Object.assign(Object.assign({}, originalGameState), { chatLog: originalGameState.chatLog || {} });
        // 更新剧情摘要（如果 Agent C 提供了）
        if ((_a = agentCResult.updatedGameState) === null || _a === void 0 ? void 0 : _a.plotSummary) {
            updatedGameState.novel.plotSummary = agentCResult.updatedGameState.plotSummary;
        }
        // 更新章节信息（如果 Agent C 提供了）
        if ((_b = agentCResult.updatedGameState) === null || _b === void 0 ? void 0 : _b.chaptersInfo) {
            // 这里可以根据需要更新章节列表
        }
        // 更新角色数据（如果 Agent C 提供了）
        if (agentCResult.updatedCharacters && agentCResult.updatedCharacters.length > 0) {
            updatedGameState.characters = agentCResult.updatedCharacters;
        }
        return updatedGameState;
    }
    /**
     * 将GameSaveState转换为GameState（用于Agent调用）
     */
    convertToGameState(gameSaveState) {
        const { chatLog } = gameSaveState, gameState = __rest(gameSaveState, ["chatLog"]);
        return Object.assign(Object.assign({}, gameState), { chat: {} // Agent期望的chat属性
         });
    }
    /**
     * 初始化新游戏的主入口
     */
    initializeNewGame(gameId, sessionId, playerName) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                console.log(`[GameInitialization] 开始初始化游戏 ${gameId}, 会话 ${sessionId}`);
                // 1. 读取并解析game_meta
                const gameContext = yield this.loadGameContext(gameId, sessionId, playerName);
                // 2. 创建基础游戏状态
                const initialGameState = yield this.createInitialGameState(gameContext);
                // 3. 使用Agent A生成第一章第一节内容
                const agentAResult = yield this.generateInitialContent(gameContext, initialGameState);
                // 4. 使用Agent C更新角色状态
                const agentCResult = yield this.updateCharacterStates(gameContext, initialGameState, agentAResult);
                // 5. 保存游戏状态和小说内容
                const finalGameState = this.convertToGameSaveState(agentCResult, initialGameState);
                yield this.saveGameState(gameContext, finalGameState, agentAResult);
                // 6. 创建欢迎消息
                const welcomeMessage = yield this.createWelcomeMessage(gameContext, agentAResult, agentCResult);
                console.log(`[GameInitialization] 游戏初始化完成`);
                return {
                    success: true,
                    sessionId,
                    gameState: finalGameState,
                    formattedUIContent: [welcomeMessage],
                    suggestedOptions: agentAResult.suggestedOptions || ["探索周围环境", "查看角色信息", "开始对话"]
                };
            }
            catch (error) {
                console.error('[GameInitialization] 初始化失败:', error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '未知错误'
                };
            }
        });
    }
    /**
     * 读取并解析游戏元数据，构建初始化上下文
     */
    loadGameContext(gameId, sessionId, playerName) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            console.log(`[GameInitialization] 读取游戏元数据: ${gameId}`);
            // 读取game_meta.json
            const gameMetaPath = path.join(db.getGameDataDir(), gameId, 'game_meta.json');
            const gameMetaContent = yield fs.readFile(gameMetaPath, 'utf-8');
            const gameMeta = JSON.parse(gameMetaContent);
            // 提取关键信息
            const worldBackground = ((_a = gameMeta.overview) === null || _a === void 0 ? void 0 : _a.background) || '';
            const initialScene = ((_b = gameMeta.overview) === null || _b === void 0 ? void 0 : _b.openingNarrative) || '';
            const characters = gameMeta.characters || [];
            const characterAttributeSchema = gameMeta.characterAttributeSchema;
            console.log(`[GameInitialization] 解析完成 - 世界背景长度: ${worldBackground.length}, 角色数量: ${characters.length}`);
            if (characterAttributeSchema && characterAttributeSchema.groups) {
                console.log(`[GameInitialization] characterAttributeSchema 可用，包含 ${characterAttributeSchema.groups.length} 个属性组`);
            }
            else {
                console.warn(`[GameInitialization] characterAttributeSchema 不可用或格式不正确`);
            }
            return {
                gameId,
                sessionId,
                playerName,
                gameMeta,
                worldBackground,
                initialScene,
                characters,
                characterAttributeSchema
            };
        });
    }
    /**
     * 创建初始游戏状态
     */
    createInitialGameState(context) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const now = new Date().toISOString();
            const sessionMeta = {
                sessionId: context.sessionId,
                gameId: context.gameId,
                gameName: ((_a = context.gameMeta.overview) === null || _a === void 0 ? void 0 : _a.worldName) || '未知游戏',
                playerName: context.playerName,
                saveName: `${context.playerName}的冒险`,
                createdAt: now,
                lastSavedAt: now,
                status: 'active'
            };
            // 变量替换函数
            const replaceVariables = (text) => {
                if (!text)
                    return text;
                return text.replace(/\{\{user\}\}/g, context.playerName);
            };
            const gameState = {
                meta: sessionMeta,
                novel: {
                    plotSummary: '',
                    currentChapter: 'chapter-1',
                    currentSection: 'chapter-1-1',
                    chapterlist: [],
                    events: []
                },
                characters: context.characters.map(char => {
                    console.log(`[GameInitialization] 处理角色: ${char.name} (${char.id})`);
                    // 转换属性格式
                    let attributeCollections = [];
                    // 优先使用 characterAttributeSchema 生成标准属性结构
                    if (context.characterAttributeSchema && context.characterAttributeSchema.groups) {
                        console.log(`[GameInitialization] 使用 characterAttributeSchema 为角色 ${char.name} 生成标准属性结构`);
                        attributeCollections = convertAttributesToCollections(char, context.characterAttributeSchema);
                        console.log(`[GameInitialization] 角色 ${char.name} 生成了 ${attributeCollections.length} 个属性组`);
                    }
                    else {
                        console.warn(`[GameInitialization] characterAttributeSchema 不可用，角色 ${char.name} 将保持原有属性格式`);
                        // 如果没有 schema，保持原有的 attributeCollections（如果有的话）
                        attributeCollections = char.attributeCollections || [];
                    }
                    return Object.assign(Object.assign({}, char), { name: replaceVariables(char.name), description: replaceVariables(char.description), isPresent: char.characterType === 'player', // 玩家角色默认在场
                        attributeCollections // 使用转换后的属性结构
                     });
                }),
                newMessage: [],
                chatLog: {}
            };
            console.log(`[GameInitialization] 创建初始游戏状态完成`);
            return gameState;
        });
    }
    /**
     * 使用Agent A生成初始内容
     */
    generateInitialContent(context, gameState) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            console.log(`[GameInitialization] 调用Agent A生成初始内容`);
            const agentAInput = {
                gameState: this.convertToGameState(gameState),
                userInput: this.buildAgentAPrompt(context)
            };
            for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
                try {
                    const result = yield this.agentManager.callAgent('agent_a', agentAInput);
                    if (result.success && result.data) {
                        console.log(`[GameInitialization] Agent A 成功生成内容 (尝试 ${attempt})`);
                        return result.data;
                    }
                    else {
                        const errorMsg = result.success ? 'No data returned' : (isErrorResult(result) ? ((_a = result.error) === null || _a === void 0 ? void 0 : _a.message) || 'Unknown error' : 'Unknown error');
                        console.warn(`[GameInitialization] Agent A 失败 (尝试 ${attempt}):`, errorMsg);
                    }
                }
                catch (error) {
                    console.error(`[GameInitialization] Agent A 调用异常 (尝试 ${attempt}):`, error);
                }
                if (attempt < this.maxRetries) {
                    yield this.delay(1000 * attempt); // 递增延迟
                }
            }
            // 所有重试都失败，使用降级策略
            console.warn(`[GameInitialization] Agent A 重试失败，使用降级策略`);
            return this.createFallbackContent(context);
        });
    }
    /**
     * 构建Agent A的提示词（专门用于游戏初始化）
     */
    buildAgentAPrompt(context) {
        var _a, _b, _c, _d;
        const { gameMeta, worldBackground, initialScene, characters } = context;
        const playerCharacter = characters.find(c => c.characterType === 'player');
        const playerName = (playerCharacter === null || playerCharacter === void 0 ? void 0 : playerCharacter.name) || context.playerName;
        const otherCharacters = characters.filter(c => c.characterType !== 'player');
        return `# 游戏初始化 - 创作第一章第一节

你是创世伙伴系统中的 Agent A (创意者)，现在需要为一个全新的游戏创作开篇内容。

## 游戏世界设定

**游戏名称：** ${((_a = gameMeta.overview) === null || _a === void 0 ? void 0 : _a.worldName) || '未知世界'}
**游戏描述：** ${((_b = gameMeta.overview) === null || _b === void 0 ? void 0 : _b.shortDescription) || ''}
**游戏标签：** ${((_d = (_c = gameMeta.overview) === null || _c === void 0 ? void 0 : _c.tags) === null || _d === void 0 ? void 0 : _d.join(', ')) || ''}

**世界背景：**
${worldBackground}

**开场叙述：**
${initialScene}

## 角色信息

**主角：**
- 姓名：${playerName}
- 类型：${(playerCharacter === null || playerCharacter === void 0 ? void 0 : playerCharacter.characterType) || 'player'}
- 描述：${(playerCharacter === null || playerCharacter === void 0 ? void 0 : playerCharacter.description) || '游戏主角'}

**其他角色：**
${otherCharacters.length > 0 ? otherCharacters.map(c => `- ${c.name} (${c.characterType}): ${c.description}`).join('\n') : '暂无其他角色'}

## 创作要求

这是游戏的**第一章第一节**，你需要：

1. **创作模式**：这是剧情创作，不是聊天
2. **内容长度**：800-1200字的精彩开篇
3. **世界观体现**：充分体现世界背景和游戏主题
4. **角色塑造**：突出主角的特点和初始状态
5. **场景设置**：基于开场叙述创作生动的初始场景
6. **剧情铺垫**：为后续发展做好铺垫
7. **行动选项**：提供3-4个符合世界观的行动选项

## 返回格式

请严格按照以下JSON格式返回：

\`\`\`json
{
  "mode": "creation",
  "narrative": "详细的第一章第一节内容，800-1200字",
  "sectionTitle": "第一节的标题",
  "sectionsId": "",
  "chaptersInfo": {
    "id": "chapter-1",
    "chapterTitle": "第一章的标题",
    "time": "时间描述",
    "location": "地点描述",
    "environment": "环境描述"
  },
  "suggestedOptions": ["选项1", "选项2", "选项3", "选项4"],
  "isPlotAdvancement": true,
  "answer": "好的，我已经为您创作了精彩的开篇内容！"
}
\`\`\`

现在请开始创作这个游戏的第一章第一节内容。`;
    }
    /**
     * 创建降级内容（当Agent A失败时使用）
     */
    createFallbackContent(context) {
        const playerCharacter = context.characters.find(c => c.characterType === 'player');
        const playerName = (playerCharacter === null || playerCharacter === void 0 ? void 0 : playerCharacter.name) || context.playerName;
        return {
            mode: 'create',
            narrative: `${playerName}的冒险即将开始。${context.initialScene || '这是一个充满未知的世界，等待着勇敢的探索者。'}`,
            suggestedOptions: ["探索周围环境", "查看角色状态", "开始对话", "查看背包"],
            sectionTitle: "冒险的开始",
            answer: "好的，让我们开始这段精彩的冒险吧！",
            chaptersInfo: {
                chapterId: "chapter-1",
                chapterTitle: "第一章 冒险的开始",
                sectionId: "chapter-1-1",
                sectionTitle: "初入世界"
            }
        };
    }
    /**
     * 使用Agent C更新角色状态
     */
    updateCharacterStates(context, gameState, agentAOutput) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            console.log(`[GameInitialization] 调用Agent C更新角色状态`);
            const agentCInput = {
                gameState: this.convertToGameState(gameState),
                agentAOutput,
                userInput: this.buildAgentCPrompt(context, agentAOutput)
            };
            for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
                try {
                    const result = yield this.agentManager.callAgent('agent_c', agentCInput);
                    if (result.success && result.data) {
                        console.log(`[GameInitialization] Agent C 成功更新状态 (尝试 ${attempt})`);
                        return result.data;
                    }
                    else {
                        const errorMsg = result.success ? 'No data returned' : (isErrorResult(result) ? ((_a = result.error) === null || _a === void 0 ? void 0 : _a.message) || 'Unknown error' : 'Unknown error');
                        console.warn(`[GameInitialization] Agent C 失败 (尝试 ${attempt}):`, errorMsg);
                    }
                }
                catch (error) {
                    console.error(`[GameInitialization] Agent C 调用异常 (尝试 ${attempt}):`, error);
                }
                if (attempt < this.maxRetries) {
                    yield this.delay(1000 * attempt);
                }
            }
            // Agent C失败，返回基础状态
            console.warn(`[GameInitialization] Agent C 重试失败，保持原有状态`);
            return {
                updatedGameState: {
                    plotSummary: gameState.novel.plotSummary
                },
                updatedCharacters: gameState.characters,
                formattedUIContent: [],
                suggestedOptions: agentAOutput.suggestedOptions || ["探索周围环境", "查看角色信息"]
            };
        });
    }
    /**
     * 构建Agent C的提示词（专门用于游戏初始化）
     */
    buildAgentCPrompt(context, agentAOutput) {
        var _a, _b, _c;
        const attributeSystem = (_a = context.gameMeta.worldSystemGuide) === null || _a === void 0 ? void 0 : _a.attributeSystem;
        const playerCharacter = context.characters.find(c => c.characterType === 'player');
        return `# 游戏初始化 - 角色状态设置

你是创世伙伴系统中的 Agent C (分析者)，现在需要为新游戏设置角色的初始状态。

## Agent A 生成的开篇内容

**章节信息：**
- 章节ID: ${((_b = agentAOutput.chaptersInfo) === null || _b === void 0 ? void 0 : _b.chapterId) || 'chapter-1'}
- 章节标题: ${((_c = agentAOutput.chaptersInfo) === null || _c === void 0 ? void 0 : _c.chapterTitle) || '第一章'}
- 小节标题: ${agentAOutput.sectionTitle || '开始'}

**叙事内容：**
${agentAOutput.narrative || ''}

## 角色DNA系统

**属性系统说明：**
${attributeSystem ? JSON.stringify(attributeSystem, null, 2) : '暂无特定属性系统'}

## 当前角色列表

${context.characters.map(c => `**${c.name}** (${c.characterType}):
- ID: ${c.id}
- 描述: ${c.description}
- 当前属性: ${JSON.stringify(c.attributes || {}, null, 2)}`).join('\n\n')}

## 初始化任务

这是游戏的第一次初始化，你需要：

1. **设置主角初始状态**：
   - 为主角 "${(playerCharacter === null || playerCharacter === void 0 ? void 0 : playerCharacter.name) || '主角'}" 设置合适的初始属性值
   - 根据世界观和角色背景确定起始数值
   - 设置在场状态为 true

2. **处理其他角色**：
   - 根据开篇内容判断哪些角色在场
   - 为在场角色设置 isPresent: true
   - 为不在场角色设置 isPresent: false

3. **识别新角色**：
   - 如果开篇内容中提到了新的重要角色，创建角色数据
   - 只为有姓名、有描述、对剧情重要的角色创建数据

4. **更新游戏状态**：
   - 设置合适的剧情摘要
   - 记录重要的状态变化

## 返回格式

请严格按照以下JSON格式返回：

\`\`\`json
{
  "analysisLog": [
    "游戏初始化：设置主角初始属性",
    "更新角色在场状态"
  ],
  "updatedGameState": {
    "plotSummary": "基于开篇内容的剧情摘要"
  },
  "updatedCharacters": [
    {
      "id": "角色ID",
      "name": "角色姓名",
      "characterType": "player/npc",
      "description": "角色描述",
      "isPresent": true/false,
      "attributeCollections": [
        {
          "id": "属性集合ID",
          "name": "属性集合名称",
          "fields": [
            {
              "id": "属性ID",
              "name": "属性名称",
              "value": "初始值",
              "type": "progress/text/list"
            }
          ]
        }
      ]
    }
  ],
  "stateChangeLog": [
    {
      "id": "角色ID",
      "msg": "状态变化描述"
    }
  ]
}
\`\`\`

现在请分析开篇内容并设置角色的初始状态。`;
    }
    /**
     * 保存游戏状态和小说内容
     */
    saveGameState(context, gameState, agentAOutput) {
        return __awaiter(this, void 0, void 0, function* () {
            console.log(`[GameInitialization] 保存游戏状态和小说内容`);
            // 1. 保存游戏状态
            yield fileSystemService.writeSession(context.gameId, context.sessionId, gameState);
            // 2. 保存角色数据
            yield fileSystemService.writeCharacters(context.gameId, context.sessionId, gameState.characters);
            // 3. 创建并保存小说内容
            const novelData = this.createNovelData(context, agentAOutput);
            const novelPath = path.join(db.getGameSavesDir(context.gameId), `novel_${context.sessionId}.json`);
            yield fs.writeFile(novelPath, JSON.stringify(novelData, null, 2), 'utf-8');
            console.log(`[GameInitialization] 游戏数据保存完成`);
        });
    }
    /**
     * 创建小说数据结构
     */
    createNovelData(context, agentAOutput) {
        var _a, _b;
        const chapterInfo = agentAOutput.chaptersInfo || {
            chapterId: "chapter-1",
            chapterTitle: "第一章",
            sectionId: "chapter-1-1",
            sectionTitle: "开始"
        };
        const section = {
            id: chapterInfo.sectionId,
            title: chapterInfo.sectionTitle,
            text: agentAOutput.narrative || '',
            timestamp: new Date().toISOString()
        };
        const chapter = {
            id: chapterInfo.chapterId,
            chapter: chapterInfo.chapterId,
            chapter_title: chapterInfo.chapterTitle,
            pre_summary: `${context.playerName}的冒险开始`,
            post_summary: ((_a = agentAOutput.narrative) === null || _a === void 0 ? void 0 : _a.substring(0, 100)) + '...' || '',
            characters: context.characters.map(c => c.id),
            sections: [section],
            backgroundImageUrl: "",
            visualPrompt: {
                description: ((_b = context.gameMeta.overview) === null || _b === void 0 ? void 0 : _b.coverImageUrl) || "游戏开始场景",
                style: "游戏风格"
            },
            time: "开始时刻",
            location: "初始位置",
            environment: context.initialScene.substring(0, 50) + '...'
        };
        return {
            chapters: [chapter]
        };
    }
    /**
     * 创建欢迎消息并保存到聊天文件
     */
    createWelcomeMessage(context, agentAOutput, agentCOutput) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            const systemMessageId = `system_${Date.now()}`;
            const timestamp = new Date().toISOString();
            // 获取主角信息
            const protagonist = (_a = context.characters) === null || _a === void 0 ? void 0 : _a.find((c) => c.characterType === 'player');
            const protagonistName = (protagonist === null || protagonist === void 0 ? void 0 : protagonist.name) || context.playerName;
            const worldName = ((_b = context.gameMeta.overview) === null || _b === void 0 ? void 0 : _b.worldName) || '游戏世界';
            let welcomeContent = `欢迎来到${worldName}！`;
            try {
                // 尝试读取小说内容，获取第一小节
                const novelService = yield import('./novel.service.js');
                const novelContent = yield novelService.getNovelContent(context.gameId, context.sessionId);
                if (novelContent && novelContent.length > 0) {
                    const firstChapter = novelContent[0];
                    if (firstChapter.sections && firstChapter.sections.length > 0) {
                        const firstSection = firstChapter.sections[0];
                        // 提取第一小节的前80字作为简短的场景描述
                        let sectionPreview = firstSection.text.substring(0, 80) + '...';
                        // 替换模板变量
                        sectionPreview = sectionPreview.replace(/\{\{user\}\}/g, protagonistName);
                        welcomeContent = `欢迎来到${worldName}！\n\n${sectionPreview}\n\n${protagonistName}，你想要做什么？`;
                        // 确保welcomeContent中的所有{{user}}都被替换
                        welcomeContent = welcomeContent.replace(/\{\{user\}\}/g, protagonistName);
                    }
                    else {
                        // 如果没有小节内容，使用默认欢迎语
                        welcomeContent = `欢迎来到${worldName}！${protagonistName}，你的冒险即将开始，接下来你要怎么做？`;
                    }
                }
                else {
                    // 如果没有小说内容，使用默认欢迎语
                    welcomeContent = `欢迎来到${worldName}！${protagonistName}，你的冒险即将开始，接下来你要怎么做？`;
                }
            }
            catch (error) {
                console.warn('[GameInitialization] 读取小说内容失败，使用默认欢迎语:', error);
                welcomeContent = `欢迎来到${worldName}！${protagonistName}，你的冒险即将开始，接下来你要怎么做？`;
            }
            // 确保所有内容都替换了{{user}}变量
            const finalWelcomeContent = welcomeContent.replace(/\{\{user\}\}/g, protagonistName);
            const finalOptions = (agentAOutput.suggestedOptions || ["探索周围环境", "查看角色信息", "开始一段对话"])
                .map(option => option.replace(/\{\{user\}\}/g, protagonistName));
            const finalSectionTitle = (agentAOutput.sectionTitle || "初始场景").replace(/\{\{user\}\}/g, protagonistName);
            const welcomeMessage = {
                id: systemMessageId,
                sender: 'system',
                content: finalWelcomeContent,
                answer: finalWelcomeContent,
                timestamp,
                options: finalOptions,
                sectionTitle: finalSectionTitle
            };
            // 保存聊天文件 - 使用新格式（多通道结构）
            const chat = { system: [welcomeMessage] };
            try {
                const fs = yield import('fs/promises');
                const path = yield import('path');
                const dbService = yield import('./db.service.js');
                const chatPath = path.join(dbService.getGameSavesDir(context.gameId), `chat_${context.sessionId}.json`);
                yield fs.writeFile(chatPath, JSON.stringify(chat, null, 2), 'utf-8');
                console.log(`[GameInitialization] 成功创建初始聊天消息: ${welcomeMessage.content.substring(0, 100)}...`);
            }
            catch (error) {
                console.error('[GameInitialization] 写入聊天文件失败:', error);
            }
            // 返回欢迎消息供调用者使用
            return welcomeMessage;
        });
    }
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
export const gameInitializationService = new GameInitializationService();
