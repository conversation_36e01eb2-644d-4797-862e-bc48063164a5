import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Users, Sparkles } from 'lucide-react';
import { Character, GameSaveState } from '../../types/gamestate';
import { useTranslation } from '../../hooks/useTranslation';
import { ImagePreview } from '../ui/ImagePreview';
import { Button } from '../ui/Button';
import { AttributeCollectionTabs } from './AttributeCollectionTabs';
import { ImageGenerationModal } from '../ui/ImageGenerationModal';
import { CharacterAvatar } from '../ui/CharacterAvatar';
import { AttributeChangeNotification } from './AttributeChangeNotification';
import { useGameStore } from '../../store/gameStore';

interface CharacterAttributePanelProps {
  character: Character;
  gameState: GameSaveState;
  gameId: string;
  sessionId: string;
}

export const CharacterAttributePanel: React.FC<CharacterAttributePanelProps> = ({
  character,
  gameState,
  gameId,
  sessionId
}) => {
  // 强制重新渲染的key
  const [renderKey, setRenderKey] = useState(0);

  // 当角色变化时强制重新渲染
  useEffect(() => {
    setRenderKey(prev => prev + 1);
  }, [character.id]);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [showImageGeneration, setShowImageGeneration] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const currentCharacterRef = useRef<HTMLDivElement>(null);

  // 从gameStore获取清除通知的方法
  const { clearCharacterNotification } = useGameStore();



  // 自动滚动到当前角色
  useEffect(() => {
    if (currentCharacterRef.current && scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const element = currentCharacterRef.current;

      const containerRect = container.getBoundingClientRect();
      const elementRect = element.getBoundingClientRect();

      // 如果当前角色不在可视区域内，滚动到它
      if (elementRect.left < containerRect.left || elementRect.right > containerRect.right) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [character.id]);

  const handleCharacterSwitch = (characterId: string) => {
    console.log('[CharacterAttributePanel] Switching to character:', characterId);
    navigate(`/game/${gameId}/${sessionId}/character/${characterId}`);
  };

  const handleGeneratePortrait = () => {
    setShowImageGeneration(true);
  };

  const handleImageGenerationSubmit = (prompt: string) => {
    console.log('生成角色立绘:', character.name, prompt);
    // 这里应该调用实际的立绘生成API
  };

  const generatePortraitPrompt = () => {
    return `${character.name}，${character.description}，全身立绘，角色设计，高质量渲染，细致的人物描绘，动漫风格，白色背景，9:16比例`;
  };

  // 检查当前角色是否有属性变化消息
  const hasAttributeChanges = useMemo(() => {
    return gameState.newMessage?.some(messageObj => messageObj[character.id]) || false;
  }, [gameState.newMessage, character.id]);

  // 处理属性变化通知的关闭
  const handleDismissNotification = async (characterId: string) => {
    try {
      await clearCharacterNotification(characterId);
    } catch (error) {
      console.error('Failed to clear character notification:', error);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900 overflow-hidden">
      {/* 角色切换标签栏 - 固定高度 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-white">{t('game.character.title')}</h2>
          <div className="text-sm text-gray-400">
            共 {gameState.characters.length} 个角色
            {gameState.characters.filter(c => c.isPresent).length > 0 && (
              <span className="ml-2">
                · {gameState.characters.filter(c => c.isPresent).length} 个在场
              </span>
            )}
          </div>
        </div>

        {/* 可滑动的角色标签容器 */}
        <div className="relative">
          <div
            ref={scrollContainerRef}
            className="flex gap-2 overflow-x-auto character-tabs-scroll pb-2"
          >
            {gameState.characters.map((char) => {
              const isCurrentCharacter = char.id === character.id;
              const isPresent = char.isPresent;

              return (
                <div
                  key={char.id}
                  ref={isCurrentCharacter ? currentCharacterRef : null}
                  onClick={() => handleCharacterSwitch(char.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all duration-200 cursor-pointer flex-shrink-0 min-w-fit ${
                    isCurrentCharacter
                      ? 'bg-pink-500/20 border-pink-500 text-pink-500'
                      : isPresent
                      ? 'bg-gray-800 border-gray-600 text-gray-300 hover:border-pink-500/50'
                      : 'bg-gray-800/50 border-gray-700 text-gray-500 hover:border-gray-500/50'
                  }`}
                  title={isPresent ? '在场角色' : '不在场角色'}
                >
                  {/* 使用CharacterAvatar组件显示属性变化特效，但不使用其点击功能 */}
                  <CharacterAvatar
                    character={char}
                    newMessages={gameState.newMessage || []}
                    size="sm"
                    className="flex-shrink-0"
                    showNotificationDot={true}
                    onClick={undefined} // 禁用CharacterAvatar的点击功能，使用外层div的点击
                    suppressTooltip={char.id === character.id && hasAttributeChanges} // 当前角色有AttributeChangeNotification时禁用tooltip
                  />

                  {/* 角色名称和状态 */}
                  <div className="flex flex-col min-w-0">
                    <span className="text-sm font-medium whitespace-nowrap">{char.name}</span>
                    {!isPresent && (
                      <span className="text-xs text-gray-500 whitespace-nowrap">(不在场)</span>
                    )}
                  </div>

                  {/* 在场状态指示器 */}
                  <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                    isPresent ? 'bg-green-400 shadow-sm shadow-green-400/50' : 'bg-gray-600'
                  }`}
                    title={isPresent ? '在场' : '不在场'}
                  />
                </div>
              );
            })}
          </div>

          {/* 滑动提示 */}
          {gameState.characters.length > 3 && (
            <div className="absolute right-0 top-0 bottom-2 w-8 bg-gradient-to-l from-gray-900 to-transparent pointer-events-none flex items-center justify-end pr-1">
              <div className="text-gray-500 text-xs">→</div>
            </div>
          )}
        </div>
      </div>

      {/* 角色立绘区域 - 固定高度 */}
      <div className="flex-shrink-0 p-4">
        <div className="h-80 bg-gray-800 rounded-lg overflow-hidden relative group">
          {character.portraitUrl ? (
            <ImagePreview
              src={character.portraitUrl}
              alt={`${character.name} ${t('game.character.portrait')}`}
              className="w-full h-full"
              showGenerateButton={true}
              onGenerateClick={handleGeneratePortrait}
            />
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center text-gray-400 p-4">
              <Users className="w-16 h-16 mb-4 opacity-50 flex-shrink-0" />
              <p className="text-lg mb-4 font-medium text-center">{character.name}</p>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleGeneratePortrait}
                className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white flex-shrink-0"
              >
                <Sparkles className="w-4 h-4" />
                <span>{t('game.character.generatePortrait')}</span>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* 动态属性区域 - 剩余空间，可滚动 */}
      <div className="flex-1 overflow-hidden min-h-0">
        <div className="h-full p-4">
          <div className="h-full overflow-y-auto custom-scrollbar space-y-4">
            {/* 属性变化通知 */}
            {hasAttributeChanges && (
              <AttributeChangeNotification
                character={character}
                newMessages={gameState.newMessage || []}
                onDismiss={handleDismissNotification}
              />
            )}

            {/* 属性集合标签页 */}
            <AttributeCollectionTabs
              key={`${character.id}-${renderKey}`}
              attributeCollections={character.attributeCollections}
            />
          </div>
        </div>
      </div>

      {/* 图片生成弹窗 */}
      <ImageGenerationModal
        isOpen={showImageGeneration}
        onClose={() => setShowImageGeneration(false)}
        initialPrompt={generatePortraitPrompt()}
        onGenerate={handleImageGenerationSubmit}
      />
    </div>
  );
};