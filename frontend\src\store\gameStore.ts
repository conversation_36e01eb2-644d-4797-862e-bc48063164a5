console.log('[DEBUG] gameStore.ts 文件已加载');
import { create } from 'zustand';
import { GameSaveState, FormattedDialogueMessage, InteractionResponse } from '../types/gamestate';
import { apiClient } from '../services/apiClient.js';

// 全局变量用于存储超时定时器
let interactionLockTimeout: number | null = null;

// 本地存储键
const STORAGE_KEY_SESSION_ID = 'genesis_partner_session_id';
const STORAGE_KEY_GAME_ID = 'genesis_partner_game_id';

// 从localStorage获取存储的会话ID
const getStoredSessionId = (): string | null => {
  return localStorage.getItem(STORAGE_KEY_SESSION_ID);
};

// 从localStorage获取存储的游戏ID
const getStoredGameId = (): string | null => {
  return localStorage.getItem(STORAGE_KEY_GAME_ID);
};

// 存储会话ID到localStorage
const storeSessionId = (sessionId: string): void => {
  localStorage.setItem(STORAGE_KEY_SESSION_ID, sessionId);
};

// 存储游戏ID到localStorage
const storeGameId = (gameId: string): void => {
  localStorage.setItem(STORAGE_KEY_GAME_ID, gameId);
};

// 清除存储的会话和游戏ID
const clearStoredIds = (): void => {
  localStorage.removeItem(STORAGE_KEY_SESSION_ID);
  localStorage.removeItem(STORAGE_KEY_GAME_ID);
};

// 强制重置交互状态
const forceResetInteractingState = (set: any): void => {
  if (interactionLockTimeout) {
    clearTimeout(interactionLockTimeout);
    interactionLockTimeout = null;
  }
  console.log('[DEBUG] forceResetInteractingState: 强制重置isInteracting和isLoading为false');
  set({ isInteracting: false, interactionLoading: false });
};

// 检查API响应中的gameState，确保不会意外地将gameState设置为null
const safeUpdateGameState = (result: any, currentState: GameSaveState | null): GameSaveState | null => {
  // 如果API返回的updatedGameState是undefined或null，保持当前状态
  if (!result.updatedGameState) {
    console.log('[DEBUG] safeUpdateGameState: API返回的updatedGameState为空，保持当前状态');
    return currentState;
  }
  
  // 如果API返回的updatedGameState是字符串'undefined'，保持当前状态
  if (result.updatedGameState === 'undefined') {
    console.log('[DEBUG] safeUpdateGameState: API返回的updatedGameState为字符串"undefined"，保持当前状态');
    return currentState;
  }
  
  // 返回API的新状态
  console.log('[DEBUG] safeUpdateGameState: 使用API返回的新gameState');
  return result.updatedGameState;
};

/**
 * 生成标准化的消息ID
 * @param sender 发送者类型 ('ai', 'user', 'system', 'character_id')
 * @param timestamp 时间戳（可选，默认为当前时间）
 * @returns 格式化的消息ID: sender_timestamp
 */
const generateStandardMessageId = (sender: string, timestamp?: number | string): string => {
  // 如果提供的是ISO字符串时间戳，转换为数字
  if (typeof timestamp === 'string') {
    timestamp = new Date(timestamp).getTime();
  }
  
  // 如果没有提供时间戳或时间戳无效，使用当前时间
  const finalTimestamp = (timestamp && !isNaN(Number(timestamp))) 
    ? Number(timestamp) 
    : Date.now();
  
  return `${sender}_${finalTimestamp}`;
};

/**
 * 确保消息对象有answer字段
 * @param message 消息对象
 * @returns 处理后的消息对象
 */
const ensureMessageAnswer = (message: FormattedDialogueMessage): FormattedDialogueMessage => {
  // 提取聊天框应该显示的内容
  const extractChatContent = (content: any): string => {
    if (typeof content === 'string') {
      // 尝试解析JSON字符串
      try {
        const parsed = JSON.parse(content);
        return extractChatContent(parsed);
      } catch {
        // 如果不是JSON，直接返回字符串
        return content;
      }
    }

    if (content && typeof content === 'object') {
      // 优先显示answer（AI回答）- 这是用户真正想听到的内容
      if (content.answer) {
        return content.answer;
      }

      // 用户消息显示ask
      if (content.ask) {
        return content.ask;
      }

      // 最后才显示thinking（AI思考过程）- 仅用于调试
      if (content.thinking) {
        return content.thinking;
      }
    }

    return '';
  };

  // 如果消息已经有answer字段，直接返回
  if (message.answer) {
    return message;
  }

  // 如果是AI消息且没有answer字段，从content中提取合适的内容
  if (message.sender === 'ai' && message.content) {
    const chatContent = extractChatContent(message.content);
    return { ...message, answer: chatContent };
  }

  // 如果是系统消息且没有answer字段，从content中提取合适的内容
  if (message.sender === 'system' && message.content) {
    const chatContent = extractChatContent(message.content);
    return { ...message, answer: chatContent };
  }

  // 如果是用户消息且没有answer字段，从content中提取合适的内容
  if (message.sender === 'user' && message.content) {
    const chatContent = extractChatContent(message.content);
    return { ...message, answer: chatContent };
  }

  return message;
};

interface GameStore {
  // 状态
  gameState: GameSaveState | null;
  initialLoading: boolean;     // 首次加载状态，用于显示全屏加载页面
  interactionLoading: boolean; // 交互加载状态，用于显示局部加载指示器
  isInteracting: boolean;
  currentSelectedGameId: string | null;
  currentSelectedSessionId: string | null;
  formattedUIContentQueue: FormattedDialogueMessage[];
  suggestedOptions: string[];
  error: string | null;
  gameId: string | null;
  sessionId: string | null;
  currentCharacterId: string | null;
  chatMap: { [id: string]: FormattedDialogueMessage[] };
  novelRefreshTrigger: number; // 用于触发小说内容重新加载

  // Actions
  loadGameSession: (gameId: string, sessionId: string) => Promise<GameSaveState | null>;
  createNewSession: (gameId: string, playerName: string, saveName?: string) => Promise<{ sessionId: string }>;
  interact: (
    gameId: string,
    sessionId: string,
    input: string | { type: string; [key: string]: any },
    mode?: 'director' | 'participant',
    targetCharacterId?: string
  ) => Promise<InteractionResponse>;
  processFormattedUIContentQueue: () => void;
  clearError: () => void;
  resetGameState: () => void;
  triggerNovelRefresh: () => void; // 触发小说内容刷新
  
  // 新增：从localStorage获取存储的ID
  getStoredIds: () => { gameId: string | null, sessionId: string | null };

  // 新增：加载聊天消息
  loadChatMessages: (targetId: string) => Promise<FormattedDialogueMessage[]>;

  // 新增：设置游戏ID
  setGameId: (id: string) => void;

  // 新增：设置会话ID
  setSessionId: (id: string) => void;

  // 新增：加载游戏状态
  loadGameState: () => Promise<void>;

  // 新增：设置当前角色ID
  setCurrentCharacterId: (id: string | null) => void;

  // 新增：发送用户输入
  sendUserInput: (input: string) => Promise<void>;

  // 新增：发送角色交互
  sendCharacterInteraction: (characterId: string, input: string) => Promise<void>;

  // 加载游戏会话
  executeInteraction: (
    gameId: string, 
    sessionId: string, 
    input: string, 
    mode: 'director' | 'participant', 
    characterId?: string
  ) => Promise<InteractionResponse>;
  
  // 新增：设置交互状态
  setIsInteracting: (isInteracting: boolean) => void;

  // 新增：更新小说位置
  updateNovelLocation: (
    gameId: string,
    sessionId: string,
    chapterId: string,
    sectionId: string,
    chapterTitle?: string,
    sectionTitle?: string
  ) => Promise<void>;

  // 新增：清除角色属性变化通知
  clearCharacterNotification: (characterId: string) => Promise<void>;

  // 兼容旧代码的isLoading getter
  get isLoading(): boolean;
}

export const useGameStore = create<GameStore>((set, get) => ({
  // 初始状态
  gameState: null,
  initialLoading: false,
  interactionLoading: false,
  isInteracting: false,
  currentSelectedGameId: getStoredGameId(),
  currentSelectedSessionId: getStoredSessionId(),
  formattedUIContentQueue: [],
  suggestedOptions: [],
  error: null,
  gameId: null,
  sessionId: null,
  currentCharacterId: null,
  chatMap: {},
  novelRefreshTrigger: 0, // 初始值为0

  // 兼容旧代码的isLoading getter
  get isLoading(): boolean {
    // 直接返回false，确保不会显示全屏加载页面
    return false;
  },

  // Actions
  loadGameSession: async (gameId: string, sessionId: string) => {
    try {
      set({ initialLoading: true, error: null });

      console.log(`Loading game session: ${gameId}/${sessionId}`);
      const response = await apiClient.loadGame({ gameId, sessionId });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to load game');
      }

      const gameState = response.data;

      console.log('gameStore: 收到的gameState:', gameState);
      console.log('gameStore: gameState.characters:', gameState?.characters);
      console.log('gameStore: gameState.novel:', gameState?.novel);

      // TODO: 加载chat数据以提取options (getChatMessages方法暂未实现)
      let extractedOptions: string[] = [];
      // try {
      //   const chatData = await apiClient.getChatMessages(gameId, sessionId, 'system');
      //   // ... 提取options的逻辑
      // } catch (chatError) {
      //   console.warn('Failed to load chat options, continuing without them:', chatError);
      // }

      // 更新状态
      set({
        gameState,
        initialLoading: false,
        gameId,
        sessionId,
        suggestedOptions: extractedOptions
      });

      // 调试：检查设置后的状态
      console.log('gameStore: 状态已更新');
      console.log('gameStore: gameState.characters.length:', gameState.characters?.length);
      if (gameState.characters) {
        gameState.characters.forEach((char: any, index: number) => {
          console.log(`gameStore: Character ${index} (${char.name}):`, {
            id: char.id,
            hasAttributeCollections: !!char.attributeCollections,
            attributeCollectionsCount: char.attributeCollections?.length || 0
          });
        });
      }

      // 存储 ID 到 localStorage
      localStorage.setItem('gameId', gameId);
      localStorage.setItem('sessionId', sessionId);

      return gameState;
    } catch (error) {
      console.error('Failed to load game session:', error);
      set({ initialLoading: false, error: 'Failed to load game session' });
      return null;
    }
  },

  createNewSession: async (gameId: string, playerName: string, saveName?: string): Promise<{ sessionId: string }> => {
    set({
      initialLoading: true,
      error: null
    });

    try {
      console.log('[DEBUG] createNewSession: 开始创建游戏会话，这可能需要1-2分钟...');

      // 调用API创建新会话
      const response = await apiClient.createGame({
        gameId,
        playerName,
        saveName
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create game session');
      }

      const result = response.data;
      
      // 存储新的sessionId到localStorage
      storeGameId(gameId);
      storeSessionId(result.sessionId);
      
      // 从初始UI内容中提取选项
      let extractedOptions: string[] = [];
      
      // 使用 API 返回的 formattedUIContent
      if (result.formattedUIContent && result.formattedUIContent.length > 0) {
        const initialSystemMsg = result.formattedUIContent[0];
        if (initialSystemMsg.options && initialSystemMsg.options.length > 0) {
          extractedOptions = initialSystemMsg.options;
        }
        console.log('初始系统消息:', initialSystemMsg);
      } else {
        // 如果 API 没有返回 formattedUIContent，使用 suggestedOptions
        extractedOptions = result.suggestedOptions || [];
      }
      
      console.log('createNewSession - 提取的选项:', extractedOptions);
      
      // 设置游戏状态
      set({ 
        gameState: result.gameState,
        suggestedOptions: extractedOptions,
        initialLoading: false,
        currentSelectedGameId: gameId,
        currentSelectedSessionId: result.sessionId
      });
      
      return { sessionId: result.sessionId };
    } catch (error) {
      console.error('Failed to create new session:', error);
      set({ 
        error: '创建新游戏会话失败，请稍后重试。',
        initialLoading: false 
      });
      throw error;
    }
  },

  // 修改 executeInteraction 函数，移除 interactionLog 相关代码
  executeInteraction: async (
    gameId: string, 
    sessionId: string, 
    input: string, 
    mode: 'director' | 'participant', 
    characterId?: string
  ) => {
    try {
      // 注释掉这一行，让外部调用控制isLoading状态
      // 因为在sendUserInput和sendCharacterInteraction中已经设置isInteracting
      // set({ isLoading: true });

      console.log(`[DEBUG] executeInteraction: 开始处理交互 mode=${mode}`);
      const response = await apiClient.interact({
        gameId,
        sessionId,
        prompt: input,
        characterId
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Interaction failed');
      }

      const result = response.data;
      console.log(`[DEBUG] executeInteraction: 交互请求完成，正在更新游戏状态`);
      console.log(`[DEBUG] executeInteraction: result.updatedGameState =`, result.updatedGameState ? '有值' : '无值');

      // 更新状态 - 使用safeUpdateGameState函数安全地更新gameState
      set((state) => ({ 
        // 使用safeUpdateGameState函数安全地更新gameState
        gameState: safeUpdateGameState(result, state.gameState),
        interactionLoading: false
      }));

      console.log(`[DEBUG] executeInteraction: 游戏状态更新完成`);
      return result;
    } catch (error) {
      console.error('Failed to execute interaction:', error);
      set({ interactionLoading: false, error: 'Failed to execute interaction' });
      throw error;
    }
  },

  // 修改 interact 函数，支持新的交互类型
  interact: async (gameId: string, sessionId: string, input: string | { type: string; [key: string]: any }, mode: 'director' | 'participant' = 'director', characterId?: string) => {
    // 强制重置交互状态，确保不会因为之前的错误导致状态被锁定
    forceResetInteractingState(set);

    try {
      console.log('[DEBUG] gameStore.interact: 开始交互', { gameId, sessionId, input, mode, characterId });
      console.log('[DEBUG] interact: set({ isInteracting: true, interactionLoading: true }) 被调用', new Error().stack);
      set({ isInteracting: true, interactionLoading: true });

      let result: InteractionResponse;

      // 处理不同类型的交互
      if (typeof input === 'object' && input.type) {
        // 处理特殊交互类型
        switch (input.type) {
          case 'switch_chapter':
            // 章节切换
            await get().updateNovelLocation(gameId, sessionId, input.chapterId, 'section-1', '', '');
            // 重新加载游戏状态
            const updatedGameState = await get().loadGameSession(gameId, sessionId);
            result = {
              updatedGameState: updatedGameState!,
              formattedUIContent: [],
              suggestedOptions: []
            };
            break;
          default:
            throw new Error(`未知的交互类型: ${input.type}`);
        }
      } else {
        // 标准文本交互
        result = await get().executeInteraction(gameId, sessionId, input as string, mode, characterId);
      }

      console.log('[DEBUG] gameStore.interact: 交互完成，当前gameState =', get().gameState ? '有值' : '无值');

      // 提取选项
      const suggestedOptions = result.suggestedOptions || [];
      console.log('[DEBUG] gameStore.interact: 从后端接收到的建议选项:', suggestedOptions);

      set(state => ({
        suggestedOptions,
        isInteracting: false,  // 确保交互完成后重置状态
        interactionLoading: false       // 确保同时重置loading状态
      }));

      console.log('[DEBUG] gameStore.interact: 交互完成，返回结果');
      console.log('[DEBUG] gameStore.interact: 交互完成后，interactionLoading =', get().interactionLoading);
      console.log('[DEBUG] gameStore.interact: 交互完成后，gameState =', get().gameState ? '有值' : '无值');

      // 触发小说内容刷新
      get().triggerNovelRefresh();
      console.log('[DEBUG] gameStore.interact: 触发小说内容刷新');

      return result;
    } catch (error) {
      console.error('[ERROR] gameStore.interact: 交互出错', error);
      set({ isInteracting: false, interactionLoading: false });  // 确保错误时也重置状态
      throw error;
    } finally {
      // 无论成功或失败，都确保重置交互状态
      console.log('[DEBUG] gameStore.interact: finally块执行，重置isInteracting和interactionLoading');
      set({ isInteracting: false, interactionLoading: false });
      console.log('[DEBUG] gameStore.interact: finally块执行后，interactionLoading =', get().interactionLoading);
      console.log('[DEBUG] gameStore.interact: finally块执行后，gameState =', get().gameState ? '有值' : '无值');
    }
  },

  processFormattedUIContentQueue: () => {
    const { formattedUIContentQueue } = get();
    
    if (formattedUIContentQueue.length === 0) return;

    // 逐条处理队列中的消息，这里可以添加打字机效果的逻辑
    // 目前简单地清空队列，实际的消息已经在gameState中了
    set({ formattedUIContentQueue: [] });
  },

  clearError: () => {
    set({ error: null });
  },

  resetGameState: () => {
    // 清除localStorage中的ID
    clearStoredIds();
    
    set({
      gameState: null,
      initialLoading: false,
      interactionLoading: false,
      isInteracting: false,
      currentSelectedGameId: null,
      currentSelectedSessionId: null,
      formattedUIContentQueue: [],
      suggestedOptions: [],
      error: null,
      gameId: null,
      sessionId: null,
      currentCharacterId: null,
      chatMap: {}
    });
  },

  triggerNovelRefresh: () => {
    set((state) => ({
      novelRefreshTrigger: state.novelRefreshTrigger + 1
    }));
  },

  // 新增：获取存储的ID
  getStoredIds: () => ({
    gameId: getStoredGameId(),
    sessionId: getStoredSessionId()
  }),

  // 新增：加载聊天消息
  loadChatMessages: async (targetId: string): Promise<FormattedDialogueMessage[]> => {
    const { gameId, sessionId } = get();
    if (!gameId || !sessionId) {
      console.error('Cannot load chat messages: gameId or sessionId is missing');
      return [];
    }

    try {
      console.log(`[GameStore] loadChatMessages called for target: ${targetId}`);

      const response = await fetch(`/api/games/${gameId}/session/${sessionId}/chat/${targetId}`);
      if (!response.ok) {
        console.error(`Failed to load chat messages: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      console.log(`[GameStore] Loaded ${data.messages?.length || 0} messages for target: ${targetId}`);

      // 转换为前端期望的格式
      const formattedMessages: FormattedDialogueMessage[] = (data.messages || []).map((msg: any) => {
        // 处理不同的消息格式
        let content;
        if (typeof msg.content === 'string') {
          // 如果content是字符串，转换为对象格式
          content = {
            answer: msg.content,
            options: msg.options || []
          };
        } else if (msg.content && typeof msg.content === 'object') {
          // 如果content已经是对象，直接使用
          content = msg.content;
        } else {
          // 兜底处理
          content = {
            answer: msg.answer || '',
            options: msg.options || []
          };
        }

        return {
          id: msg.id,
          sender: msg.sender,
          content,
          timestamp: msg.timestamp,
          characterId: msg.characterId,
          isNarrative: msg.sender === 'system' && !msg.characterId
        };
      });

      // 如果是系统消息，从最新的系统消息中提取建议选项
      if (targetId === 'system' && formattedMessages.length > 0) {
        // 找到最新的系统消息（从后往前找）
        for (let i = formattedMessages.length - 1; i >= 0; i--) {
          const msg = formattedMessages[i];
          // 检查多种可能的选项位置：直接的options字段或content.options
          const options = msg.options || (msg.content?.options) || [];
          if (msg.sender === 'system' && Array.isArray(options) && options.length > 0) {
            console.log(`[GameStore] 从聊天消息中提取建议选项:`, options);
            set({ suggestedOptions: options });
            break;
          }
        }
      }

      return formattedMessages;
    } catch (error) {
      console.error('Failed to load chat messages:', error);
      return [];
    }
  },

  // 新增：设置游戏ID
  setGameId: (id: string) => {
    set({ gameId: id });
  },

  // 新增：设置会话ID
  setSessionId: (id: string) => {
    set({ sessionId: id });
  },

  // 新增：加载游戏状态
  loadGameState: async () => {
    const { gameId, sessionId } = get();
    if (!gameId || !sessionId) {
      console.error('Cannot load game state: gameId or sessionId is missing');
      return;
    }

    try {
      set({ initialLoading: true });
      const response = await apiClient.loadGame({ gameId, sessionId });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to load game');
      }

      const gameState = response.data;

      // TODO: 加载chat数据以提取options (getChatMessages方法暂未实现)
      // try {
      //   const chatData = await apiClient.getChatMessages(gameId, sessionId, 'ai');
      //   // ... 提取options的逻辑
      // } catch (chatError) {
      //   console.warn('Failed to load chat options, continuing without them:', chatError);
      // }

      set({
        gameState,
        initialLoading: false,
        suggestedOptions: [] // 暂时使用空数组
      });
    } catch (error) {
      console.error('Failed to load game state:', error);
      set({ initialLoading: false, error: 'Failed to load game state' });
    }
  },

  // 新增：设置当前角色ID
  setCurrentCharacterId: (id: string | null) => {
    set({ currentCharacterId: id });
  },

  // 新增：发送用户输入
  sendUserInput: async (input: string): Promise<void> => {
    // 强制重置交互状态，确保不会因为之前的错误导致状态被锁定
    forceResetInteractingState(set);
    
    const { gameId, sessionId } = get();
    console.log('[DEBUG] sendUserInput: 输入参数', { gameId, sessionId, input });
    console.log('[DEBUG] sendUserInput: 当前isInteracting状态:', get().isInteracting);

    if (!gameId || !sessionId) {
      console.error('[ERROR] sendUserInput: 缺少 gameId 或 sessionId，提前 return', { gameId, sessionId });
      return;
    }

    if (get().isInteracting) {
      console.warn('[WARN] sendUserInput: 已在交互中，提前 return');
      console.warn('[WARN] sendUserInput: isInteracting=true 阻止了请求发送');
      return;
    }

    if (!input || !input.trim()) {
      console.warn('[WARN] sendUserInput: 输入为空，提前 return');
      return;
    }

    set({ isInteracting: true, interactionLoading: true });
    console.log('[DEBUG] sendUserInput: 设置 isInteracting = true');

    const unlockTimeout = setTimeout(() => {
      set({ isInteracting: false, interactionLoading: false });
      console.error('[ERROR] sendUserInput: 超时自动解锁 isInteracting');
    }, 30000); // 增加到30秒超时

    try {
      console.log('[DEBUG] sendUserInput: try 开始');
      // 在发送请求前，先在本地添加用户消息
      const timestamp = Date.now();
      const userMessageId = generateStandardMessageId('user', timestamp);
      set(state => {
        const aiChat = state.chatMap['ai'] || [];
        return {
          chatMap: {
            ...state.chatMap,
            'ai': [
              ...aiChat,
              {
                id: userMessageId,
                sender: 'user',
                content: { ask: input },  // 修复：使用正确的content结构
                ask: input,
                timestamp: new Date(timestamp).toISOString()
              }
            ]
          }
        };
      });
      console.log('[DEBUG] sendUserInput: 本地消息已添加，准备发起请求');
      console.log('[DEBUG] sendUserInput: 调用interact前，isInteracting =', get().isInteracting);
      const response = await get().interact(gameId, sessionId, input, 'director');
      console.log('[DEBUG] sendUserInput: interact 返回', response);
      console.log('[DEBUG] sendUserInput: interact完成后，isInteracting =', get().isInteracting);
      console.log('[DEBUG] sendUserInput: 当前gameState =', get().gameState ? '有值' : '无值');
      if (response.formattedUIContent && response.formattedUIContent.length > 0) {
        set(state => {
          const aiChat = state.chatMap['ai'] || [];
          const processedContent = response.formattedUIContent.map(msg => {
            let updatedMsg = { ...msg };
            if (!msg.id || !msg.id.includes('_')) {
              updatedMsg.id = generateStandardMessageId(msg.sender, msg.timestamp);
            }
            updatedMsg = ensureMessageAnswer(updatedMsg);
            return updatedMsg;
          });
          return {
            chatMap: {
              ...state.chatMap,
              'ai': [...aiChat, ...processedContent]
            },
            suggestedOptions: response.suggestedOptions || [],
            interactionLoading: false // 确保响应处理后重置loading状态
          };
        });
      } else {
        await get().loadChatMessages('ai');
        // 确保在加载消息后重置loading状态
        set({ interactionLoading: false });
      }
      console.log('[DEBUG] sendUserInput: try 结束');
    } catch (error) {
      console.error('[ERROR] sendUserInput: catch 捕获异常', error);
      // 确保错误时也重置loading状态
      set({ interactionLoading: false });
    } finally {
      clearTimeout(unlockTimeout); // 清除超时
      set({ isInteracting: false, interactionLoading: false });
      console.log('[DEBUG] sendUserInput: finally 重置 isInteracting=false, interactionLoading=false');
      console.log('[DEBUG] sendUserInput: finally后，isInteracting =', get().isInteracting);
    }
  },
  
  sendCharacterInteraction: async (characterId: string, input: string): Promise<void> => {
    // 强制重置交互状态，确保不会因为之前的错误导致状态被锁定
    forceResetInteractingState(set);
    
    const { gameId, sessionId } = get();
    if (!gameId || !sessionId) {
      console.error('Cannot send character interaction: gameId or sessionId is missing');
      return;
    }
    
    // 检查是否已经在交互中
    if (get().isInteracting) {
      console.log('[DEBUG] gameStore.sendCharacterInteraction: 已经在交互中，忽略请求');
      return;
    }
    
    // 明确设置交互状态为true
    set({ isInteracting: true, interactionLoading: true });
    console.log('[DEBUG] gameStore.sendCharacterInteraction: 设置交互状态为true');
    
    // === 新增：超时自动解锁 ===
    const unlockTimeout = setTimeout(() => {
      set({ isInteracting: false, interactionLoading: false });
      console.error('[ERROR] sendCharacterInteraction: 超时自动解锁 isInteracting');
    }, 15000); // 15秒超时

    try {
      // 在发送请求前，先在本地添加用户消息
      const timestamp = Date.now();
      const userMessageId = generateStandardMessageId('user', timestamp);

      // 更新本地聊天状态
      set(state => {
        const characterChat = state.chatMap[characterId] || [];
        return {
          chatMap: {
            ...state.chatMap,
            [characterId]: [
              ...characterChat,
              {
                id: userMessageId,
                sender: 'user',
                content: { ask: input },  // 修复：使用正确的content结构
                ask: input,
                timestamp: new Date(timestamp).toISOString()
              }
            ]
          }
        };
      });
      
      // 发送交互请求
      console.log('[DEBUG] gameStore.sendCharacterInteraction: 发送交互请求');
      const response = await get().interact(gameId, sessionId, input, 'participant', characterId);
      console.log('[DEBUG] gameStore.sendCharacterInteraction: 交互请求完成');
      
      // 如果有响应内容，直接添加到聊天中，避免再次请求
      if (response.formattedUIContent && response.formattedUIContent.length > 0) {
        set(state => {
          const characterChat = state.chatMap[characterId] || [];
          // 确保所有消息都有answer字段和标准化ID
          const processedContent = response.formattedUIContent.map(msg => {
            // 标准化ID
            let updatedMsg = { ...msg };
            if (!msg.id || !msg.id.includes('_')) {
              updatedMsg.id = generateStandardMessageId(msg.sender, msg.timestamp);
            }
            // 确保有answer字段
            updatedMsg = ensureMessageAnswer(updatedMsg);
            return updatedMsg;
          });
          
          return {
            chatMap: {
              ...state.chatMap,
              [characterId]: [...characterChat, ...processedContent]
            },
            suggestedOptions: response.suggestedOptions || []
          };
        });
      } else {
        // 如果没有直接返回内容，则重新加载聊天消息
        await get().loadChatMessages(characterId);
      }
    } catch (error) {
      console.error('[ERROR] sendCharacterInteraction: 发送角色交互失败', error);
    } finally {
      clearTimeout(unlockTimeout); // 清除超时
      set({ isInteracting: false, interactionLoading: false });
      console.log('[DEBUG] sendCharacterInteraction: finally 重置 isInteracting=false, interactionLoading=false');
    }
  },

  // 新增：设置交互状态
  setIsInteracting: (isInteracting: boolean) => {
    console.log('[DEBUG] setIsInteracting 被调用', isInteracting, new Error().stack);
    
    // 如果存在之前的超时定时器，清除它
    if (interactionLockTimeout) {
      clearTimeout(interactionLockTimeout);
      interactionLockTimeout = null;
    }
    
    // 如果设置为true，创建一个新的超时定时器
    if (isInteracting) {
      interactionLockTimeout = setTimeout(() => {
        console.log('[DEBUG] isInteracting 超时自动解锁');
        set({ isInteracting: false, interactionLoading: false });
      }, 30000); // 30秒超时
    }
    
    // 同时设置isInteracting和interactionLoading，保持状态同步
    set({ isInteracting, interactionLoading: isInteracting });
  },

  updateNovelLocation: async (
    gameId: string,
    sessionId: string,
    chapterId: string,
    sectionId: string,
    chapterTitle?: string,
    sectionTitle?: string
  ) => {
    console.log(`[DEBUG] updateNovelLocation: Updating to chapterId=${chapterId}, sectionId=${sectionId}`);
    set({ interactionLoading: true, error: null }); // Indicate loading state
    try {
      // TODO: 实现updateNovelSceneReference方法
      // const response = await apiClient.updateNovelSceneReference(gameId, sessionId, {
      //   chapterId,
      //   chapterTitle,
      //   sceneId: sectionId,
      //   sceneName: sectionTitle,
      // });

      // 暂时模拟成功响应
      const response = { success: true };

      if (response.success) {
        console.log('[DEBUG] updateNovelLocation: API call successful, updating local state.');
        set((state) => {
          if (state.gameState && state.gameState.novel) {
            return {
              interactionLoading: false,
              gameState: {
                ...state.gameState,
                novel: {
                  ...state.gameState.novel,
                  currentChapter: chapterId,
                  currentSection: sectionId,
                  // Potentially update chapterTitle and sectionName here if they exist in gameState.novel
                },
              },
            };
          }
          return { interactionLoading: false }; // Should not happen if called correctly
        });
        // Optionally, trigger a full gameState refresh if needed for consistency,
        // but optimistic update is generally preferred for responsiveness.
        // await get().loadGameSession(gameId, sessionId);
      } else {
        console.error('[ERROR] updateNovelLocation: API call failed', response.message);
        set({
          interactionLoading: false,
          error: `Failed to update novel location: ${response.message || 'Unknown error'}`
        });
      }
    } catch (error: any) {
      console.error('[ERROR] updateNovelLocation: Exception during API call', error);
      set({
        interactionLoading: false,
        error: `Error updating novel location: ${error.message || 'Network error'}`
      });
      // Re-throw or handle as appropriate for the application flow
    }
  },

  // 清除角色属性变化通知
  clearCharacterNotification: async (characterId: string) => {
    const { gameId, sessionId } = get();
    if (!gameId || !sessionId) {
      console.error('[ERROR] clearCharacterNotification: Missing gameId or sessionId');
      return;
    }

    try {
      console.log(`[GameStore] 清除角色 ${characterId} 的属性变化通知`);

      const response = await fetch(`/api/games/${gameId}/session/${sessionId}/clear-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ characterId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.gameState) {
        // 更新游戏状态
        set({ gameState: data.gameState });
        console.log(`[GameStore] 已清除角色 ${characterId} 的属性变化通知`);
      }
    } catch (error: any) {
      console.error('[ERROR] clearCharacterNotification:', error);
      set({
        error: `清除通知失败: ${error.message || 'Network error'}`
      });
    }
  }
}));