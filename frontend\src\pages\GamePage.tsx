import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGameStore } from '../store/gameStore';
import { Character } from '../types/gamestate';
import { LoadingScreen } from '../components/game/LoadingScreen';
import { StoryPanel } from '../components/game/StoryPanel';
import { CreatorPanel } from '../components/game/CreatorPanel';
import { GameHeader } from '../components/game/GameHeader';
import { GroupChatPanel } from '../components/game/GroupChatPanel';

export const GamePage: React.FC = () => {
  const { gameId, sessionId } = useParams<{ gameId: string; sessionId: string }>();
  const navigate = useNavigate();
  const {
    gameState,
    initialLoading,
    error,
    loadGameSession,
    getStoredIds,
    currentCharacterId,
    setCurrentCharacterId,
    updateNovelLocation // Destructure the new action
  } = useGameStore();

  const [showChatPanel, setShowChatPanel] = useState<boolean>(false);
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null);
  const [renderKey, setRenderKey] = useState(0);

  // 监听路由变化，确保状态正确
  useEffect(() => {
    // 当路由变化时，检查是否需要重置状态
    const urlPath = window.location.pathname;
    const isDirectorMode = !urlPath.includes('/character/');

    if (isDirectorMode && currentCharacterId) {
      console.log('[GamePage] 检测到返回导演模式，重置角色状态');
      setCurrentCharacterId(null);
      setCurrentCharacter(null);
      setShowChatPanel(false);
      setRenderKey(prev => prev + 1);
    }
  }, [window.location.pathname, currentCharacterId]);

  // 使用ref来追踪是否已经加载过
  const loadedRef = React.useRef<string | null>(null);

  useEffect(() => {
    const sessionKey = `${gameId}-${sessionId}`;

    const loadSession = async () => {
      if (gameId && sessionId && loadedRef.current !== sessionKey) {
        // 优先使用URL中的sessionId，只有当加载失败时才考虑localStorage中的备用sessionId
        console.log(`Loading game session: ${gameId}/${sessionId}`);

        try {
          await loadGameSession(gameId, sessionId);
          loadedRef.current = sessionKey;
        } catch (error) {
          console.error(`Failed to load session ${sessionId}, checking localStorage backup...`);

          // 如果URL中的session加载失败，尝试使用localStorage中的备用session
          const { gameId: storedGameId, sessionId: storedSessionId } = getStoredIds();

          if (storedSessionId && storedSessionId !== sessionId) {
            console.log(`尝试重定向到存储的sessionId: ${storedSessionId}`);
            navigate(`/game/${gameId}/${storedSessionId}`);
            return;
          }

          // 如果都失败了，显示错误
          console.error('No valid session found');
        }
      } else if (!gameId || !sessionId) {
        navigate('/');
      }
    };

    loadSession();
  }, [gameId, sessionId]); // 移除函数依赖，避免重复执行

  // 当进入导演模式时，清除当前角色ID并强制重新渲染
  useEffect(() => {
    // 如果URL中没有characterId，说明是在导演模式，清除currentCharacterId
    const urlPath = window.location.pathname;
    if (!urlPath.includes('/character/')) {
      console.log('[DEBUG] GamePage: 进入导演模式，清除currentCharacterId');
      setCurrentCharacterId(null);
      // 强制重新渲染
      setRenderKey(prev => prev + 1);
    }
  }, [gameId, sessionId]); // 当路由参数变化时检查

  // 当 currentCharacterId 变化时更新当前角色
  useEffect(() => {
    if (gameState && currentCharacterId) {
      const character = gameState.characters.find(c => c.id === currentCharacterId);
      setCurrentCharacter(character || null);
      setShowChatPanel(!!character);
    } else {
      setCurrentCharacter(null);
      setShowChatPanel(false);
    }
  }, [gameState, currentCharacterId]);

  // 错误处理
  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="p-6 bg-red-500/20 border border-red-500 rounded-lg text-red-400 mb-6">
            {error}
          </div>
          <button
            onClick={() => navigate('/')}
            className="button-cyber"
          >
            返回主页
          </button>
        </div>
      </div>
    );
  }

  // 加载状态 - 只有在首次加载游戏状态时显示加载页面，交互中不显示
  if (!gameState) {
    console.log('[DEBUG] GamePage: 显示加载页面，gameState未加载');
    return <LoadingScreen />;
  }

  const handleNovelLocationChange = (
    chapterIdValue: string,
    sectionIdValue: string,
    chapterTitle?: string,
    sectionTitle?: string
  ) => {
    if (gameId && sessionId) {
      console.log(
        `[DEBUG] GamePage: handleNovelLocationChange called with:
        Chapter ID: ${chapterIdValue}, Section ID: ${sectionIdValue},
        Chapter Title: ${chapterTitle}, Section Title: ${sectionTitle}`
      );
      // Call the action from the store
      updateNovelLocation(gameId, sessionId, chapterIdValue, sectionIdValue, chapterTitle, sectionTitle);
    } else {
      console.error("[ERROR] GamePage: gameId or sessionId is missing, cannot update novel location.");
    }
  };

  return (
    <div key={`game-page-${renderKey}`} className="h-screen flex flex-col overflow-hidden">
      {/* 游戏头部 */}
      <div className="flex-shrink-0">
        <GameHeader
          gameState={gameState}
          gameId={gameId!}
          sessionId={sessionId!}
        />
      </div>

      {/* 主要游戏界面 */}
      <div className="flex-1 flex min-h-0 overflow-hidden">
        {/* 左侧：故事面板 (2/3) */}
        <div className="flex-1 min-h-0 overflow-hidden" style={{ flex: '2' }}>
          <StoryPanel
            gameState={gameState}
            gameId={gameId!}
            sessionId={sessionId!}
            onNovelLocationChange={handleNovelLocationChange}
          />
        </div>

        {/* 右侧：创作者面板 (1/3) */}
        <div className="border-l border-gray-700 min-h-0 overflow-hidden" style={{ flex: '1' }}>
          <CreatorPanel
            gameState={gameState}
            gameId={gameId!}
            sessionId={sessionId!}
          />
        </div>
      </div>

      {/* 右侧聊天面板 */}
      {showChatPanel && currentCharacter && gameId && sessionId && (
        <GroupChatPanel
          gameState={gameState}
          currentCharacter={currentCharacter}
          gameId={gameId}
          sessionId={sessionId}
        />
      )}
    </div>
  );
};