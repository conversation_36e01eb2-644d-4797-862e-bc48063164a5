import {
  GameBlueprint,
  GameSaveState,
  SessionMeta,
  InteractionResponse,
  FormattedDialogueMessage,
  SystemError,
  OpenAIConfig,
  SystemConfig
} from '../../../shared/types/core.js';
import { CreatorStudioFormData, AvailableModels } from '../types/creatorStudio';
import { ValidationResult } from '../../../shared/types/validation.js';

/**
 * 创建系统错误对象
 */
function createSystemError(type: SystemError['type'], message: string, context?: Record<string, any>): SystemError {
  return {
    type,
    message,
    recoverable: type !== 'unknown',
    timestamp: Date.now(),
    context
  };
}

/**
 * 改进的API响应处理函数
 */
async function handleResponse<T>(response: Response): Promise<ValidationResult<T>> {
  try {
    if (!response.ok) {
      // 尝试解析错误响应为JSON
      let errorData;
      let errorText;

      try {
        errorData = await response.json();
        errorText = errorData.message || JSON.stringify(errorData);
      } catch (e) {
        // 如果无法解析为JSON，则读取为文本
        errorText = await response.text();
      }

      // 根据HTTP状态码确定错误类型
      let errorType: SystemError['type'] = 'unknown';
      if (response.status >= 400 && response.status < 500) {
        errorType = 'validation';
      } else if (response.status >= 500) {
        errorType = 'network';
      }

      return {
        success: false,
        error: `API 错误 (${response.status}): ${errorText}`,
        issues: [{
          code: `http_${response.status}`,
          message: errorText,
          path: [response.url]
        }]
      };
    }

    const data = await response.json() as T;
    return { success: true, data };

  } catch (error: any) {
    return {
      success: false,
      error: `网络错误: ${error.message}`,
      issues: [{
        code: 'network_error',
        message: error.message,
        path: [response.url]
      }]
    };
  }
}

/**
 * 兼容性包装器 - 保持向后兼容
 */
async function handleResponseLegacy<T>(response: Response): Promise<T> {
  const result = await handleResponse<T>(response);
  if (!result.success) {
    const error = new Error(result.error);
    (error as any).response = {
      status: response.status,
      data: result.issues
    };
    throw error;
  }
  return result.data!;
}

// 系统配置类型已从共享类型导入

export const apiClient = {
  // P-00 主页相关 API
  async listGameBlueprints(noCache: boolean = false): Promise<GameBlueprint[]> {
    console.log("API: Fetching game blueprints...");

    try {
      // 添加随机查询参数以避免缓存
      const url = noCache
        ? `/api/game-blueprints?_t=${Date.now()}`
        : '/api/game-blueprints';

      const response = await fetch(url);
      return handleResponseLegacy<GameBlueprint[]>(response);
    } catch (error: any) {
      console.error('[API] 获取游戏蓝图失败:', error);
      throw error;
    }
  },

  async createGameBlueprint(
    blueprintData: Partial<GameBlueprint>, 
    initialGameMeta: {
      playerName: string;
      initialSceneName?: string;
      initialSceneDescription?: string;
      initialSceneImageUrl?: string;
      initialCharacters?: any[];
    }
  ): Promise<{ gameId: string; sessionId: string }> {
    console.log("API: Creating new game blueprint...");
    
    const response = await fetch('/api/game/session/new', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ blueprintData, initialGameMeta })
    });

    return handleResponse<{ gameId: string; sessionId: string }>(response);
  },

  // Creator Studio 相关 API
  async getAvailableModels(): Promise<AvailableModels> {
    console.log("API: Fetching available AI models...");
    const response = await fetch('/api/creator-studio/models');
    return handleResponse<AvailableModels>(response);
  },

  async generateGameWorld(input: {
    worldName: string;
    shortDescription: string;
    background: string;
  }): Promise<{ formData: CreatorStudioFormData; message: string }> {
    console.log("API: Generating game world with AI...");
    const response = await fetch('/api/creator-studio/generate-world', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(input)
    });
    return handleResponse<{ formData: CreatorStudioFormData; message: string }>(response);
  },

  async loadGameForEdit(gameId: string): Promise<CreatorStudioFormData> {
    console.log(`API: Loading game for edit: ${gameId}`);
    const response = await fetch(`/api/creator-studio/game/${gameId}`);
    const result = await handleResponse<CreatorStudioFormData>(response);

    if (!result.success) {
      throw new Error(result.error || '加载游戏数据失败');
    }

    console.log('✅ Loaded game data:', result.data);
    return result.data!;
  },



  // 将地点信息转换为场景信息
  transformLocationsToScenes(locations: any[]): any[] {
    return locations.map((location, index) => ({
      id: `scene_${index + 1}`,
      name: location.name || `场景 ${index + 1}`,
      narrativeDescription: location.description || location.significance || '',
      visualPrompt: `${location.name}, ${location.description}`,
      isInitial: index === 0 // 第一个地点作为初始场景
    }));
  },

  // 从角色数据中提取属性系统架构
  extractAttributeSchemaFromCharacters(characters: any[]): any {
    if (!characters || characters.length === 0) {
      return { groups: [] };
    }

    // 从第一个角色的 attributeCollections 中提取架构
    const firstCharacter = characters[0];
    if (!firstCharacter.attributeCollections) {
      return { groups: [] };
    }

    const groups = firstCharacter.attributeCollections.map((collection: any) => ({
      id: collection.name || collection.id || 'group_1',
      name: collection.name || '属性组',
      fields: (collection.attributes || []).map((attr: any, index: number) => ({
        id: `field_${index + 1}`,
        name: attr.name || '属性',
        displayType: 'text',
        description: attr.description || ''
      }))
    }));

    return { groups };
  },

  async createOrUpdateGame(
    formData: CreatorStudioFormData,
    gameId?: string | null
  ): Promise<{ gameId: string; sessionId: string | null }> {
    console.log("API: Creating/Updating game with Creator Studio data...");

    const url = gameId
      ? `/api/creator-studio/game/${gameId}`
      : '/api/creator-studio/game';

    const method = gameId ? 'PUT' : 'POST';

    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formData)
    });

    return handleResponse<{ gameId: string; sessionId: string | null }>(response);
  },

  async saveAndLaunchGame(
    formData: CreatorStudioFormData,
    playerName: string,
    gameId?: string | null
  ): Promise<{ gameId: string; sessionId: string }> {
    console.log("API: Saving and launching game...");

    const response = await fetch('/api/creator-studio/game/save-and-launch', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        formData,
        gameId,
        playerName
      })
    });

    return handleResponse<{ gameId: string; sessionId: string }>(response);
  },

  async getInstructions(): Promise<string> {
    console.log("API: Fetching game instructions...");
    const response = await fetch('/api/instructions');
    return handleResponse<string>(response);
  },

  async listSessionSaves(gameId: string): Promise<SessionMeta[]> {
    console.log(`API: Listing session saves for game: ${gameId}`);
    const response = await fetch(`/api/games/${gameId}/sessions`);
    return handleResponse<SessionMeta[]>(response);
  },

  async loadGameSession(gameId: string, sessionId: string): Promise<GameSaveState> {
    console.log(`API: Loading game session: ${sessionId}`);
    const response = await fetch(`/api/game/${gameId}/session/${sessionId}`);
    const result = await handleResponse<{ gameState: any }>(response);
    console.log('API: 收到的完整响应:', result);
    console.log('API: gameState结构:', Object.keys(result.gameState || {}));
    console.log('API: gameState.characters类型:', typeof result.gameState?.characters);

    // 详细检查每个角色的数据
    if (result.gameState?.characters && Array.isArray(result.gameState.characters)) {
      console.log('API: 后端返回的角色数据详情:');
      result.gameState.characters.forEach((char: any, index: number) => {
        console.log(`  角色 ${index}: ${char.name} (${char.id})`);
        console.log(`    - attributeCollections: ${char.attributeCollections ? char.attributeCollections.length : 'undefined'}`);
        console.log(`    - isPresent: ${char.isPresent}`);
        if (char.attributeCollections) {
          char.attributeCollections.forEach((collection: any, colIndex: number) => {
            console.log(`      集合 ${colIndex}: ${collection.name || collection.label} (${collection.fields?.length || 0} 个字段)`);
          });
        }
      });
    }

    // 数据适配：将后端的新格式转换为前端期望的格式
    const adaptedGameState = await this.adaptGameStateFormat(result.gameState);
    console.log('API: 适配后的角色数据:', adaptedGameState.characters?.length, '个角色');
    return adaptedGameState;
  },

  // 数据适配器：将后端的新数据格式转换为前端期望的格式
  async adaptGameStateFormat(backendGameState: any): Promise<GameSaveState> {
    console.log('API: adaptGameStateFormat - 输入数据:', backendGameState);

    // 处理角色数据
    let characters: any[] = [];

    if (backendGameState.characters && typeof backendGameState.characters === 'object') {
      console.log('API: 角色数据存在，类型:', typeof backendGameState.characters);
      console.log('API: 是否为数组:', Array.isArray(backendGameState.characters));

      if (Array.isArray(backendGameState.characters)) {
        // 如果已经是数组，直接使用
        characters = backendGameState.characters;
        console.log('API: 直接使用角色数组，共', characters.length, '个角色');

        // 调试：检查每个角色的数据完整性
        characters.forEach((char, index) => {
          console.log(`API: Character ${index} (${char.name}):`, {
            id: char.id,
            hasAttributeCollections: !!char.attributeCollections,
            attributeCollectionsCount: char.attributeCollections?.length || 0,
            isPresent: char.isPresent
          });
        });
      } else if (backendGameState.characters.present) {
        // 如果是新格式 {present: string[], total: number}，需要获取角色详情
        try {
          const gameId = backendGameState.meta?.gameId;
          if (gameId) {
            const gameMetaResponse = await fetch(`/api/creator-studio/game/${gameId}`);
            const gameMeta = await gameMetaResponse.json();

            if (gameMeta.characters && Array.isArray(gameMeta.characters)) {
              // 加载所有角色，并根据present数组设置isPresent属性
              characters = gameMeta.characters.map((char: any) => ({
                ...char,
                isPresent: backendGameState.characters.present.includes(char.id)
              }));

              console.log(`Successfully loaded ${characters.length} characters from game meta (${backendGameState.characters.present.length} present)`);
            }
          }
        } catch (error) {
          console.error('Failed to fetch character details from game meta:', error);
          characters = [];
        }
      }
    }

    return {
      meta: backendGameState.meta || {},
      novel: backendGameState.novel || {
        plotSummary: '',
        currentChapter: '',
        currentSection: '',
        chapterlist: [],
        events: []
      },
      characters: characters,
      newMessage: backendGameState.newMessage || [],
      chatLog: backendGameState.chatLog || {}
    };
  },

  async interact(
    gameId: string, 
    sessionId: string, 
    prompt: string, 
    mode: 'director' | 'participant', 
    targetCharacterId?: string
  ): Promise<InteractionResponse> {
    console.log(`[DEBUG] apiClient: Interacting with session ${sessionId} in ${mode} mode`);
    console.log('[DEBUG] apiClient: Request payload:', { prompt, mode, characterId: targetCharacterId });
    
    // 验证参数
    if (!gameId || !sessionId || !prompt) {
      console.error('[ERROR] apiClient: Missing required parameters for interact', { gameId, sessionId, prompt });
      throw new Error('Missing required parameters for interaction');
    }
    
    // 构建请求体
    const payload = {
      prompt,
      mode,
      ...(targetCharacterId && { characterId: targetCharacterId })
    };
    
    // 确保请求体是有效的JSON
    let payloadString: string;
    try {
      payloadString = JSON.stringify(payload);
      console.log('[DEBUG] apiClient: 请求体 (JSON):', payloadString);
      // 验证JSON是否有效
      JSON.parse(payloadString);
    } catch (jsonError) {
      console.error('[ERROR] apiClient: 无效的JSON请求体:', jsonError);
      throw new Error('Invalid JSON payload');
    }
    
    try {
      const url = `/api/game/${gameId}/session/${sessionId}/interact`;
      console.log(`[DEBUG] apiClient: Sending POST request to ${url}`);
      console.log('[DEBUG] apiClient: Request payload (stringified):', payloadString);
      
      // 添加时间戳，避免缓存
      const timestampedUrl = `${url}?_t=${Date.now()}`;
      
      const response = await fetch(timestampedUrl, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        body: payloadString,
        // 确保请求不会被缓存
        cache: 'no-store'
      });
      
      console.log('[DEBUG] apiClient: Received response status:', response.status, response.statusText);
      console.log('[DEBUG] apiClient: Response headers:', Object.fromEntries([...response.headers.entries()]));
      
      if (!response.ok) {
        console.error(`[ERROR] apiClient: HTTP error ${response.status}`, response.statusText);
        // 尝试读取错误响应
        try {
          const errorText = await response.text();
          console.error('[ERROR] apiClient: Error response body:', errorText);
        } catch (readError) {
          console.error('[ERROR] apiClient: Could not read error response body');
        }
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }
      
      const responseText = await response.text();
      console.log('[DEBUG] apiClient: Raw response text:', responseText.substring(0, 200) + '...');
      
      let result;
      try {
        result = JSON.parse(responseText) as InteractionResponse;
      } catch (parseError) {
        console.error('[ERROR] apiClient: Failed to parse JSON response:', parseError);
        console.error('[ERROR] apiClient: Invalid JSON:', responseText);
        throw new Error('Invalid JSON response from server');
      }
      
      console.log('[DEBUG] apiClient: Parsed response data:', {
        updatedGameState: result.updatedGameState ? '(GameState object)' : 'undefined',
        formattedUIContent: result.formattedUIContent?.length || 0,
        suggestedOptions: result.suggestedOptions?.length || 0
      });
      
      return result;
    } catch (error) {
      console.error('[ERROR] apiClient: Error in interact request:', error);
      throw error;
    }
  },

  async generateImage(prompt: string, style?: string): Promise<string> {
    console.log("API: Generating image...");
    
    const response = await fetch('/api/generate-image', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt, style })
    });

    const result = await handleResponse<{ imageUrl: string }>(response);
    return result.imageUrl;
  },

  // 系统配置相关 API
  async getSystemConfig(): Promise<SystemConfig> {
    console.log("API: Fetching system configuration...");
    const response = await fetch('/api/system/config');
    return handleResponse<SystemConfig>(response);
  },

  async updateSystemConfig(config: SystemConfig): Promise<{ message: string; config: SystemConfig }> {
    console.log("API: Updating system configuration...");
    const response = await fetch('/api/system/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    });
    return handleResponse<{ message: string; config: SystemConfig }>(response);
  },

  async updateAgentConfig(
    agentType: 'default' | 'agent_a' | 'agent_b' | 'agent_c', 
    config: Partial<OpenAIConfig>
  ): Promise<{ message: string; config: SystemConfig }> {
    console.log(`API: Updating ${agentType} configuration...`);
    const response = await fetch(`/api/system/config/agent/${agentType}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    });
    return handleResponse<{ message: string; config: SystemConfig }>(response);
  },

  async updateAllAgentsConfig(
    config: Partial<OpenAIConfig>
  ): Promise<{ message: string; config: SystemConfig }> {
    console.log("API: Updating all agents configuration...");
    const response = await fetch('/api/system/config/agents/all', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    });
    return handleResponse<{ message: string; config: SystemConfig }>(response);
  },

  // 测试模型连接
  async testModelConnection(
    apiKey: string, 
    baseUrl: string, 
    model: string
  ): Promise<{ success: boolean; message: string; modelResponse?: string }> {
    console.log("API: Testing model connection...");
    const response = await fetch('/api/system/config/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ apiKey, baseUrl, model })
    });
    return handleResponse<{ success: boolean; message: string; modelResponse?: string }>(response);
  },
  
  // 修复游戏数据编码问题
  async fixGameDataEncoding(): Promise<{ 
    success: boolean; 
    message: string; 
    details?: { 
      totalGames: number; 
      processedGames: Array<{id: string; name: string}>; 
      failedGames: Array<{id: string; error: string}> 
    } 
  }> {
    console.log("API: Fixing game data encoding...");
    const response = await fetch('/api/system/fix-encoding', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    return handleResponse<{ 
      success: boolean; 
      message: string; 
      details?: { 
        totalGames: number; 
        processedGames: Array<{id: string; name: string}>; 
        failedGames: Array<{id: string; error: string}> 
      } 
    }>(response);
  },

  /**
   * 上传封面图片
   * @param imageData 图片的Base64数据
   * @param gameId 可选的游戏ID
   */
  async uploadCoverImage(imageData: string, gameId?: string): Promise<{
    filename: string;
    url: string;
    message: string;
  }> {
    console.log("API: 上传封面图片...");
    const response = await fetch('/api/creator-studio/upload-cover', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageData, gameId }),
    });

    return handleResponse<{ filename: string; url: string; message: string }>(response);
  },

  /**
   * 创建新的游戏会话
   * @param gameId 游戏ID
   * @param playerName 玩家名称
   * @param saveName 可选的存档名称
   */
  async createNewGameSession(
    gameId: string, 
    playerName: string, 
    saveName?: string
  ): Promise<{ 
    sessionId: string; 
    gameState: GameSaveState;
    formattedUIContent?: FormattedDialogueMessage[];
    suggestedOptions?: string[];
  }> {
    console.log("API: Creating new game session...");
    
    const response = await fetch('/api/game/session/simple-new', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        gameId,
        playerName,
        saveName
      })
    });

    return handleResponse<{ 
      sessionId: string; 
      gameState: GameSaveState;
      formattedUIContent?: FormattedDialogueMessage[];
      suggestedOptions?: string[];
    }>(response);
  },

  /**
   * 删除游戏存档
   * @param gameId 游戏ID
   * @param sessionId 会话ID
   */
  async deleteGameSession(gameId: string, sessionId: string): Promise<{ message: string; deletedSessionId: string }> {
    console.log(`API: Deleting game session: ${sessionId}`);

    const response = await fetch(`/api/games/${gameId}/session/${sessionId}`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });

    return handleResponse<{ message: string; deletedSessionId: string }>(response);
  },

  /**
   * 删除游戏及其所有相关文件
   * @param gameId 游戏ID
   */
  async deleteGame(gameId: string): Promise<{ message: string; deletedGameId: string }> {
    console.log(`API: Deleting game: ${gameId}`);
    const response = await fetch(`/api/game-blueprints/${gameId}`, {
      method: 'DELETE'
    });
    return handleResponse<{ message: string; deletedGameId: string }>(response);
  },
  
  // 新增：获取聊天消息
  async getChatMessages(gameId: string, sessionId: string, targetId: string): Promise<{ messages: FormattedDialogueMessage[] }> {
    console.log(`API: Fetching chat messages for target: ${targetId}`);
    const response = await fetch(`/api/game/${gameId}/session/${sessionId}/chat/${targetId}`);
    return handleResponse<{ messages: FormattedDialogueMessage[] }>(response);
  },
  
  // 新增：发送游戏输入（导演模式）
  async sendGameInput(gameId: string, sessionId: string, data: { prompt: string }): Promise<InteractionResponse> {
    console.log(`API: Sending game input in director mode`);
    return this.interact(gameId, sessionId, data.prompt, 'director');
  },
  
  // 新增：发送角色交互（参与者模式）
  async sendCharacterInteraction(gameId: string, sessionId: string, characterId: string, input: string): Promise<InteractionResponse> {
    console.log(`API: Sending character interaction in participant mode`);
    return this.interact(gameId, sessionId, input, 'participant', characterId);
  },

  async updateNovelSceneReference(
    gameId: string,
    sessionId: string,
    data: { sceneId: string; sceneName?: string; chapterId: string; chapterTitle?: string }
  ): Promise<{ success: boolean; message?: string }> {
    console.log(`API: Updating novel scene reference for session: ${sessionId}`);
    const response = await fetch(`/api/game/${gameId}/session/${sessionId}/update-scene`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    // The backend might return { success: true, message: "..." } or just { success: true }
    // or on error, it might have a message. Adjust handleResponse if needed or assume it handles this.
    return handleResponse<{ success: boolean; message?: string }>(response);
  }
}; 