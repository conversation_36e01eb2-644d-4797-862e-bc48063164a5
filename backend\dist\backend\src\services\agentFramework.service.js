/**
 * Agent链式调用框架
 * 使用现代化的设计模式，提供健壮的Agent调用管理
 *
 * 特性：
 * - 类型安全的Agent定义
 * - 链式调用管理
 * - 错误处理和重试机制
 * - 性能监控和日志
 * - 可扩展的Agent插件系统
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { z } from 'zod';
import { OpenAI } from 'openai';
import { jsonrepair } from 'jsonrepair';
import * as configService from './config.service.js';
// ===== Agent基类 =====
export class BaseAgent {
    constructor(config) {
        this.config = config;
        // 先设置一个临时的OpenAI客户端，真正的配置在initializeOpenAI中设置
        this.openai = new OpenAI({
            baseURL: 'https://api.openai.com/v1',
            apiKey: 'dummy-key'
        });
        // 异步初始化真正的配置
        this.initializationPromise = this.initializeOpenAI();
    }
    initializeOpenAI() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const systemConfig = yield configService.readLegacyConfig();
                // 根据Agent ID选择对应的配置
                let modelConfig;
                if (this.config.id === 'agent_a' && systemConfig.openai.agent_a) {
                    modelConfig = systemConfig.openai.agent_a;
                }
                else if (this.config.id === 'agent_b' && systemConfig.openai.agent_b) {
                    modelConfig = systemConfig.openai.agent_b;
                }
                else if (this.config.id === 'agent_c' && systemConfig.openai.agent_c) {
                    modelConfig = systemConfig.openai.agent_c;
                }
                else {
                    modelConfig = systemConfig.openai.default;
                }
                // 更新Agent配置中的模型名称
                this.config.model = modelConfig.model;
                // 加载提示词模板
                yield this.loadPromptTemplate();
                console.log(`[Agent:${this.config.id}] 使用配置:`, {
                    baseURL: modelConfig.baseUrl,
                    model: modelConfig.model,
                    hasApiKey: !!modelConfig.apiKey,
                    apiKeyPrefix: modelConfig.apiKey ? modelConfig.apiKey.substring(0, 10) + '...' : 'none'
                });
                // 重新创建OpenAI客户端，使用真实配置
                this.openai = new OpenAI({
                    baseURL: modelConfig.baseUrl,
                    apiKey: modelConfig.apiKey || 'dummy-key'
                });
                console.log(`[Agent:${this.config.id}] OpenAI客户端已更新`);
            }
            catch (error) {
                console.error(`[Agent:${this.config.id}] 初始化OpenAI客户端失败:`, error);
                // 使用默认配置
                this.openai = new OpenAI({
                    baseURL: 'https://api.openai.com/v1',
                    apiKey: 'dummy-key'
                });
            }
        });
    }
    loadPromptTemplate() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const fs = yield import('fs/promises');
                const path = yield import('path');
                // 构建提示词文件路径
                const promptFileName = `${this.config.id}_prompt.md`;
                const promptFilePath = path.join(process.cwd(), '..', 'gamedata', 'prompts', promptFileName);
                // 读取提示词文件
                const promptContent = yield fs.readFile(promptFilePath, 'utf-8');
                // 更新配置中的提示词模板
                this.config.promptTemplate = promptContent;
                console.log(`[Agent:${this.config.id}] 提示词模板已加载: ${promptFileName}`);
            }
            catch (error) {
                console.error(`[Agent:${this.config.id}] 加载提示词模板失败:`, error);
                // 使用默认的简单提示词
                this.config.promptTemplate = `You are ${this.config.name}. Please respond in JSON format based on the input.`;
            }
        });
    }
    callOpenAI(prompt, context) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            const startTime = Date.now();
            try {
                // 确保初始化完成
                yield this.initializationPromise;
                console.log(`[Agent:${this.config.id}] 开始调用OpenAI，模型: ${this.config.model}`);
                const response = yield this.openai.chat.completions.create({
                    model: this.config.model,
                    messages: [{ role: 'user', content: prompt }],
                    temperature: 0.7,
                    max_tokens: 8000
                });
                const result = ((_b = (_a = response.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const executionTime = Date.now() - startTime;
                console.log(`[Agent:${this.config.id}] OpenAI调用成功，耗时: ${executionTime}ms`);
                return result;
            }
            catch (error) {
                const executionTime = Date.now() - startTime;
                // 处理速率限制错误
                if (error.status === 429) {
                    console.warn(`[Agent:${this.config.id}] API速率限制，耗时: ${executionTime}ms`);
                    throw new Error('API_RATE_LIMIT');
                }
                console.error(`[Agent:${this.config.id}] OpenAI调用失败，耗时: ${executionTime}ms`, error);
                throw error;
            }
        });
    }
    validateInput(input) {
        if (!this.config.inputSchema)
            return true;
        const result = this.config.inputSchema.safeParse(input);
        if (!result.success) {
            console.error(`[Agent:${this.config.id}] 输入验证失败:`, result.error);
            return false;
        }
        return true;
    }
    validateOutput(output) {
        if (!this.config.outputSchema)
            return true;
        const result = this.config.outputSchema.safeParse(output);
        if (!result.success) {
            console.error(`[Agent:${this.config.id}] 输出验证失败:`, result.error);
            return false;
        }
        return true;
    }
    buildPrompt(template, context, variables = {}) {
        let prompt = template;
        // 添加强制JSON格式要求到提示词末尾
        const jsonFormatInstruction = `

=== CRITICAL INSTRUCTION ===
YOU ARE A JSON OUTPUT MACHINE. YOU CAN ONLY OUTPUT JSON. NO EXCEPTIONS.

FORBIDDEN RESPONSES:
- "好的" / "明白了" / "我会" / "我将"
- Any explanations or greetings
- Markdown code blocks
- Any text before or after JSON

REQUIRED FORMAT:
- Start immediately with {
- End with }
- Pure JSON only
- No other characters

EXAMPLE OF CORRECT RESPONSE:
{"updatedGameState":{},"updatedCharacters":[],"stateChangeLog":[]}

NOW OUTPUT ONLY JSON:`;
        prompt += jsonFormatInstruction;
        // 替换上下文变量
        prompt = prompt.replace(/\{\{gameId\}\}/g, context.gameId);
        prompt = prompt.replace(/\{\{sessionId\}\}/g, context.sessionId);
        prompt = prompt.replace(/\{\{userInput\}\}/g, context.userInput);
        // 替换自定义变量
        for (const [key, value] of Object.entries(variables)) {
            const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
            prompt = prompt.replace(regex, String(value));
        }
        return prompt;
    }
    parseJsonResponse(response) {
        // 清理AI响应，提取JSON部分
        let cleanResponse = response.trim();
        // 移除常见的中文开头
        cleanResponse = cleanResponse.replace(/^(好的[，。]?|以下是[，。]?|这里是[，。]?|根据[^{]*[，。]?)/g, '');
        // 移除markdown代码块标记
        cleanResponse = cleanResponse.replace(/^```json\s*/g, '');
        cleanResponse = cleanResponse.replace(/\s*```$/g, '');
        // 查找第一个 { 和最后一个 }
        const firstBrace = cleanResponse.indexOf('{');
        const lastBrace = cleanResponse.lastIndexOf('}');
        if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
            cleanResponse = cleanResponse.substring(firstBrace, lastBrace + 1);
        }
        try {
            return JSON.parse(cleanResponse);
        }
        catch (error) {
            console.error(`[Agent:${this.config.id}] JSON解析失败，原始响应:`, response);
            console.error(`[Agent:${this.config.id}] 清理后响应:`, cleanResponse);
            // 尝试使用 jsonrepair 修复 JSON
            try {
                console.log(`[Agent:${this.config.id}] 尝试使用 jsonrepair 修复 JSON...`);
                const repairedJson = jsonrepair(cleanResponse);
                console.log(`[Agent:${this.config.id}] JSON 修复成功:`, repairedJson);
                return JSON.parse(repairedJson);
            }
            catch (repairError) {
                console.error(`[Agent:${this.config.id}] JSON 修复也失败:`, repairError);
                throw error; // 抛出原始错误
            }
        }
    }
}
// ===== 具体Agent实现 =====
// Agent A输出Schema - 根据agent_a_prompt.md定义
const AgentAOutputSchema = z.object({
    mode: z.string(),
    narrative: z.string().optional(), // 创作模式时必填，聊天模式时可选
    sectionTitle: z.string().optional(), // 创作模式时必填
    sectionsId: z.string().optional(), // 重写时有值，新增时为空
    chaptersInfo: z.object({
        id: z.string(),
        chapterTitle: z.string(),
        time: z.string(),
        location: z.string(),
        environment: z.string()
    }).optional().nullable(), // 仅在有新章节时输出，可以为null
    suggestedOptions: z.array(z.string()),
    isPlotAdvancement: z.boolean().optional(), // 创作模式时有值
    answer: z.string() // 始终必填
});
export class AgentA extends BaseAgent {
    constructor() {
        super({
            id: 'agent_a',
            name: 'Narrative Generator',
            description: '生成叙事内容和建议选项',
            promptTemplate: '', // 将从文件加载
            model: 'gemini-2.0-flash',
            maxRetries: 3,
            timeout: 30000,
            outputSchema: AgentAOutputSchema
        });
    }
    process(context, userPrompt) {
        return __awaiter(this, void 0, void 0, function* () {
            const startTime = Date.now();
            try {
                console.log(`[AgentA] 开始处理用户输入: ${userPrompt}`);
                // 确保初始化完成
                yield this.initializationPromise;
                console.log(`[Agent:${this.config.id}] 提示词模板长度: ${this.config.promptTemplate.length}`);
                console.log(`[Agent:${this.config.id}] 提示词模板开头: ${this.config.promptTemplate.substring(0, 100)}...`);
                // 构建提示词，让AI自己判断模式
                const prompt = this.buildPrompt(this.config.promptTemplate, context, {
                    userPrompt,
                    currentState: JSON.stringify(context.currentState, null, 2)
                });
                // 调用OpenAI
                console.log(`[Agent:${this.config.id}] 发送给AI的提示词:`, prompt.substring(0, 500) + '...');
                const response = yield this.callOpenAI(prompt, context);
                // 解析响应
                const parsed = this.parseJsonResponse(response);
                console.log(`[Agent:${this.config.id}] 解析后的JSON:`, JSON.stringify(parsed, null, 2));
                // 验证输出
                if (!this.validateOutput(parsed)) {
                    console.error(`[Agent:${this.config.id}] 验证失败的数据:`, parsed);
                    throw new Error('输出格式验证失败');
                }
                const executionTime = Date.now() - startTime;
                console.log(`[AgentA] 处理完成，耗时: ${executionTime}ms`);
                return {
                    success: true,
                    data: parsed,
                    executionTime
                };
            }
            catch (error) {
                const executionTime = Date.now() - startTime;
                console.error(`[AgentA] 处理失败，耗时: ${executionTime}ms`, error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                    executionTime
                };
            }
        });
    }
}
// Agent C输出Schema - 根据agent_c_prompt.md定义 (修复analysisLog字段和attributeCollections验证) - 重启服务器
const AgentCOutputSchema = z.object({
    analysisLog: z.array(z.string()).optional(), // 分析日志
    updatedGameState: z.object({
        chaptersInfo: z.object({
            id: z.string(),
            chapterSummary: z.string()
        }).optional(),
        plotSummary: z.string().optional()
    }).optional(),
    updatedCharacters: z.array(z.object({
        id: z.string(),
        name: z.string().optional(), // 新角色需要名称
        characterType: z.string().optional(), // 新角色需要类型
        description: z.string().optional(), // 新角色需要描述
        portraitUrl: z.string().optional(), // 新角色需要头像
        aiModels: z.object({
            imageBaseModel: z.string(),
            imageLoRAModel: z.string(),
            ttsModel: z.string(),
            voiceId: z.string()
        }).optional(), // 新角色需要AI模型配置
        isPresent: z.boolean(),
        attributeCollections: z.array(z.object({
            id: z.string().optional(),
            label: z.string().optional(),
            fields: z.array(z.object({
                id: z.string().optional(),
                label: z.string().optional(),
                type: z.string().optional(),
                value: z.any().optional(),
                reason: z.union([
                    z.string(),
                    z.array(z.object({
                        isNew: z.boolean(),
                        text: z.string()
                    }))
                ]).optional()
            })).optional()
        })).optional()
    })).optional(),
    stateChangeLog: z.array(z.object({
        id: z.string(),
        msg: z.string()
    })).optional()
});
export class AgentC extends BaseAgent {
    constructor() {
        super({
            id: 'agent_c',
            name: 'State Manager',
            description: '管理游戏状态和角色信息',
            promptTemplate: '', // 将从文件加载
            model: 'gemini-2.0-flash',
            maxRetries: 3,
            timeout: 30000,
            outputSchema: AgentCOutputSchema
        });
    }
    /**
     * 从 characterAttributeSchema 转换为 attributeCollections 格式
     */
    convertSchemaToAttributeCollections(characterAttributeSchema) {
        if (!characterAttributeSchema || !characterAttributeSchema.groups) {
            return [];
        }
        return characterAttributeSchema.groups.map((group) => ({
            id: group.id,
            label: group.name,
            fields: (group.fields || []).map((field) => ({
                id: field.id,
                label: field.name,
                type: field.displayType,
                value: this.getDefaultValueForField(field),
                reason: '基于角色设定的初始值'
            }))
        }));
    }
    /**
     * 根据字段类型和规则获取默认值
     */
    getDefaultValueForField(field) {
        const displayType = field.displayType;
        const rules = field.rules;
        switch (displayType) {
            case 'number':
                if (rules && rules.range && rules.range.default !== undefined) {
                    return rules.range.default;
                }
                return 0;
            case 'progress':
                if (rules && rules.range && rules.range.default !== undefined) {
                    return rules.range.default;
                }
                return 50; // 默认50%
            case 'starrating':
                if (rules && rules.range && rules.range.default !== undefined) {
                    return rules.range.default;
                }
                return 3; // 默认3星
            case 'tags':
                return [];
            case 'text':
            default:
                return '待设定';
        }
    }
    buildAttributeStructureExample(context) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 优先使用 characterAttributeSchema 构建标准属性结构
                if (context.characterAttributeSchema && context.characterAttributeSchema.groups) {
                    console.log('[AgentC] 使用 characterAttributeSchema 构建属性结构示例');
                    const standardAttributeCollections = this.convertSchemaToAttributeCollections(context.characterAttributeSchema);
                    if (standardAttributeCollections.length > 0) {
                        const structureExample = {
                            "标准角色属性结构（基于meta蓝图）": {
                                "id": "char_X",
                                "name": "角色名称",
                                "characterType": "npc",
                                "description": "角色描述",
                                "portraitUrl": "",
                                "aiModels": {
                                    "imageBaseModel": "stable-diffusion-xl",
                                    "imageLoRAModel": "fantasy-style",
                                    "ttsModel": "azure-neural",
                                    "voiceId": "zh-CN-XiaoyuNeural"
                                },
                                "isPresent": true,
                                "attributeCollections": standardAttributeCollections
                            }
                        };
                        return `
## 标准角色属性结构（基于游戏meta蓝图）

请严格按照以下结构为新角色创建属性：

\`\`\`json
${JSON.stringify(structureExample, null, 2)}
\`\`\`

**重要**：
- 新角色必须包含与meta蓝图定义完全一致的attributeCollections结构
- 所有属性组和字段都必须包含，不可遗漏
- 只需要调整具体的属性值，不要改变结构
- 确保包含所有必需的基本字段（id, name, characterType, description等）
- 属性值必须符合meta蓝图中定义的类型和范围要求
`;
                    }
                }
                // 回退方案：从现有角色获取属性结构
                console.log('[AgentC] characterAttributeSchema不可用，回退到使用现有角色属性结构');
                const characters = context.currentState.characters || [];
                if (characters.length === 0) {
                    return "当前游戏中暂无角色，且无法获取属性架构定义，请根据游戏背景创建合适的属性结构。";
                }
                // 获取第一个角色的属性结构作为示例
                const exampleCharacter = characters[0];
                const attributeCollections = exampleCharacter.attributeCollections || [];
                if (attributeCollections.length === 0) {
                    return "当前角色暂无属性结构，且无法获取属性架构定义，请创建基本的属性组。";
                }
                // 构建属性结构示例（回退方案）
                const structureExample = {
                    "现有角色属性结构示例（回退方案）": {
                        "id": "char_X",
                        "name": "角色名称",
                        "characterType": "npc",
                        "description": "角色描述",
                        "portraitUrl": "",
                        "aiModels": {
                            "imageBaseModel": "stable-diffusion-xl",
                            "imageLoRAModel": "fantasy-style",
                            "ttsModel": "azure-neural",
                            "voiceId": "zh-CN-XiaoyuNeural"
                        },
                        "isPresent": true,
                        "attributeCollections": attributeCollections
                    }
                };
                return `
## 当前游戏的属性结构示例（回退方案）

⚠️ **警告**：无法获取meta蓝图的属性定义，使用现有角色结构作为回退方案。

请严格按照以下结构为新角色创建属性：

\`\`\`json
${JSON.stringify(structureExample, null, 2)}
\`\`\`

**重要**：
- 新角色必须包含与现有角色相同的attributeCollections结构
- 只需要调整具体的属性值，不要改变结构
- 确保包含所有必需的基本字段（id, name, characterType, description等）
- 注意：此结构可能不完整，建议检查meta蓝图定义
`;
            }
            catch (error) {
                console.error('[AgentC] 构建属性结构示例失败:', error);
                return "无法获取属性结构示例，请创建基本的角色属性。";
            }
        });
    }
    process(context, agentAOutput) {
        return __awaiter(this, void 0, void 0, function* () {
            const startTime = Date.now();
            try {
                console.log(`[AgentC] 开始处理Agent A输出`);
                // 确保初始化完成
                yield this.initializationPromise;
                // 动态构建属性结构示例
                const attributeStructureExample = yield this.buildAttributeStructureExample(context);
                // 构建提示词
                const prompt = this.buildPrompt(this.config.promptTemplate, context, {
                    agentAOutput,
                    currentState: JSON.stringify(context.currentState, null, 2),
                    attributeStructureExample
                });
                // 调用OpenAI
                let response;
                try {
                    response = yield this.callOpenAI(prompt, context);
                }
                catch (error) {
                    if (error.message === 'API_RATE_LIMIT') {
                        console.warn('[AgentC] API速率限制，返回默认结果');
                        // 返回默认的空结果，让系统继续运行
                        return {
                            success: true,
                            data: {
                                updatedCharacters: [],
                                stateChangeLog: []
                            }
                        };
                    }
                    throw error;
                }
                // 解析响应
                const parsed = this.parseJsonResponse(response);
                console.log(`[Agent:${this.config.id}] 解析后的JSON:`, JSON.stringify(parsed, null, 2));
                // 特别检查新角色数据
                if (parsed.updatedCharacters && Array.isArray(parsed.updatedCharacters)) {
                    parsed.updatedCharacters.forEach((char, index) => {
                        console.log(`[AgentC] 角色 ${index + 1} (${char.id || '无ID'}):`, {
                            hasName: !!char.name,
                            hasCharacterType: !!char.characterType,
                            hasDescription: !!char.description,
                            hasIsPresent: char.isPresent !== undefined,
                            hasAttributeCollections: !!char.attributeCollections
                        });
                    });
                }
                // 验证输出
                if (!this.validateOutput(parsed)) {
                    console.error(`[Agent:${this.config.id}] 验证失败的数据:`, parsed);
                    throw new Error('输出格式验证失败');
                }
                const executionTime = Date.now() - startTime;
                console.log(`[AgentC] 处理完成，耗时: ${executionTime}ms`);
                return {
                    success: true,
                    data: parsed,
                    executionTime
                };
            }
            catch (error) {
                const executionTime = Date.now() - startTime;
                console.error(`[AgentC] 处理失败，耗时: ${executionTime}ms`, error);
                return {
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                    executionTime
                };
            }
        });
    }
}
export class AgentChain {
    constructor(context) {
        this.steps = [];
        this.context = context;
    }
    addStep(step) {
        this.steps.push(step);
        return this;
    }
    execute(initialInput) {
        return __awaiter(this, void 0, void 0, function* () {
            const startTime = Date.now();
            const results = [];
            let currentInput = initialInput;
            console.log(`[AgentChain] 开始执行链式调用，共${this.steps.length}个步骤`);
            try {
                for (let i = 0; i < this.steps.length; i++) {
                    const step = this.steps[i];
                    const stepStartTime = Date.now();
                    console.log(`[AgentChain] 执行步骤 ${i + 1}/${this.steps.length}: ${step.agent.config.name}`);
                    // 准备输入
                    const input = step.inputMapper
                        ? step.inputMapper(currentInput, this.context)
                        : currentInput;
                    // 如果输入为 null，跳过这个步骤
                    if (input === null) {
                        console.log(`[AgentChain] 步骤 ${i + 1} 被跳过`);
                        const stepTime = Date.now() - stepStartTime;
                        console.log(`[AgentChain] 步骤 ${i + 1} 完成，耗时: ${stepTime}ms`);
                        // 保持 currentInput 不变，继续下一个步骤
                        continue;
                    }
                    // 执行Agent
                    const result = yield this.executeStepWithRetry(step, input);
                    results.push(result);
                    if (!result.success) {
                        // 尝试错误处理
                        if (step.errorHandler) {
                            const recoveredOutput = step.errorHandler(new Error(result.error || 'Unknown error'), this.context);
                            if (recoveredOutput) {
                                result.data = recoveredOutput;
                                result.success = true;
                                console.log(`[AgentChain] 步骤 ${i + 1} 通过错误处理器恢复`);
                            }
                        }
                        if (!result.success) {
                            console.error(`[AgentChain] 步骤 ${i + 1} 执行失败，中断链式调用`);
                            break;
                        }
                    }
                    currentInput = result.data;
                    const stepTime = Date.now() - stepStartTime;
                    console.log(`[AgentChain] 步骤 ${i + 1} 完成，耗时: ${stepTime}ms`);
                }
                const totalExecutionTime = Date.now() - startTime;
                const success = results.every(r => r.success);
                console.log(`[AgentChain] 链式调用完成，总耗时: ${totalExecutionTime}ms，成功: ${success}`);
                return {
                    success,
                    results,
                    totalExecutionTime,
                    error: success ? undefined : '链式调用中存在失败的步骤'
                };
            }
            catch (error) {
                const totalExecutionTime = Date.now() - startTime;
                console.error(`[AgentChain] 链式调用异常，总耗时: ${totalExecutionTime}ms`, error);
                return {
                    success: false,
                    results,
                    totalExecutionTime,
                    error: error instanceof Error ? error.message : String(error)
                };
            }
        });
    }
    executeStepWithRetry(step, input) {
        return __awaiter(this, void 0, void 0, function* () {
            const retryPolicy = step.retryPolicy || { maxRetries: 1, backoffMs: 1000 };
            let lastError = null;
            for (let attempt = 0; attempt < retryPolicy.maxRetries; attempt++) {
                try {
                    if (attempt > 0) {
                        console.log(`[AgentChain] 重试第 ${attempt} 次，等待 ${retryPolicy.backoffMs}ms`);
                        yield new Promise(resolve => setTimeout(resolve, retryPolicy.backoffMs));
                    }
                    const result = yield step.agent.process(this.context, input);
                    if (result.success) {
                        return result;
                    }
                    else {
                        lastError = new Error(result.error || 'Agent执行失败');
                    }
                }
                catch (error) {
                    lastError = error instanceof Error ? error : new Error(String(error));
                }
            }
            return {
                success: false,
                error: (lastError === null || lastError === void 0 ? void 0 : lastError.message) || '重试次数耗尽',
                executionTime: 0
            };
        });
    }
}
// ===== 预定义的Agent链 =====
export class DirectorModeChain {
    static create(context) {
        return __awaiter(this, void 0, void 0, function* () {
            const agentA = new AgentA();
            const agentC = new AgentC();
            // Agent实例在构造时已经自动加载了提示词模板，无需重复加载
            const chain = new AgentChain(context)
                .addStep({
                agent: agentA,
                inputMapper: (_, ctx) => ctx.userInput,
                retryPolicy: { maxRetries: 3, backoffMs: 1000 },
                errorHandler: (error, ctx) => {
                    console.warn(`[DirectorModeChain] AgentA失败，使用默认输出`, error);
                    return {
                        mode: 'creation',
                        narrative: '抱歉，我暂时无法生成内容，请稍后再试。',
                        suggestedOptions: ['重试', '继续'],
                        sectionTitle: '当前场景',
                        answer: '好的，我会继续为您服务。'
                    };
                }
            })
                .addStep({
                agent: agentC,
                inputMapper: (agentAResult, ctx) => {
                    console.log(`[DirectorModeChain] 检查 Agent A 输出模式: ${agentAResult === null || agentAResult === void 0 ? void 0 : agentAResult.mode}`);
                    // 如果是聊天模式，跳过 Agent C
                    if (agentAResult && agentAResult.mode === 'chat') {
                        console.log(`[DirectorModeChain] ✅ 检测到聊天模式，跳过 Agent C 以提升性能`);
                        return null; // 返回 null 表示跳过这个步骤
                    }
                    console.log(`[DirectorModeChain] 继续执行 Agent C 进行状态管理`);
                    return JSON.stringify(agentAResult);
                },
                retryPolicy: { maxRetries: 2, backoffMs: 1500 },
                errorHandler: (error, ctx) => {
                    console.warn(`[DirectorModeChain] AgentC失败，使用当前状态`, error);
                    return ctx.currentState;
                }
            });
            return chain;
        });
    }
}
// ===== 工厂方法 =====
export class AgentChainFactory {
    static createDirectorChain(context) {
        return __awaiter(this, void 0, void 0, function* () {
            return DirectorModeChain.create(context);
        });
    }
    static createParticipantChain(context) {
        return __awaiter(this, void 0, void 0, function* () {
            // TODO: 实现参与者模式链
            throw new Error('参与者模式链尚未实现');
        });
    }
    static createCustomChain(context) {
        return new AgentChain(context);
    }
}
