import { GameSaveState, InteractionResponse, Character, FormattedDialogueMessage, GameEvent } from '../../../frontend/src/types/gamestate.js';
import { OpenAI } from 'openai';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as fileSystemService from './fileSystem.service.js';
import * as novelService from './novel.service.js';
import * as db from './db.service.js';
import { v4 as uuidv4 } from 'uuid';
import { generateStandardMessageId, ensureMessageAnswer } from './fileSystem.service.js';
import { z } from 'zod';
import * as dataAccess from './dataAccess.service.js';
import {
  AgentChainFactory,
  AgentContext,
  ChainResult
} from './agentFramework.service.js';
import { gameDataManager } from './dataAccess.service.js';
import type { CreatorStudioFormData } from '../../../frontend/src/types/creatorStudio.js';
import { readLegacyConfig } from './config.service.js';
import { AttributeSystemService } from './attributeSystemService.js';

/**
 * 验证和补全角色数据，确保所有必需字段都存在
 */
function validateAndCompleteCharacter(character: any): any {
  let wasIncomplete = false;

  // 检查必需字段并补全
  const validated = {
    id: character.id || (() => {
      wasIncomplete = true;
      return `char_${Date.now()}`;
    })(),
    name: character.name || (() => {
      wasIncomplete = true;
      return '未知角色';
    })(),
    characterType: character.characterType || (() => {
      wasIncomplete = true;
      return 'npc';
    })(),
    description: character.description || (() => {
      wasIncomplete = true;
      return '一个神秘的角色。';
    })(),
    portraitUrl: character.portraitUrl || 'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=600',
    aiModels: character.aiModels || {
      imageBaseModel: 'stable-diffusion-xl',
      imageLoRAModel: 'fantasy-style',
      ttsModel: 'azure-neural',
      voiceId: 'zh-CN-XiaoyuNeural'
    },
    // 保留旧的attributes字段以兼容性，但不再主动生成
    attributes: character.attributes || {},
    isPresent: character.isPresent !== undefined ? character.isPresent : false,
    attributeCollections: character.attributeCollections || (() => {
      if (!character.attributeCollections) wasIncomplete = true;
      return [{
        id: 'base',
        label: '基本属性',
        fields: [
          {
            id: 'health',
            label: '生命值',
            type: 'progress',
            value: 100,
            reason: '初次登场，状态良好'
          },
          {
            id: 'mood',
            label: '心情',
            type: 'text',
            value: '友善',
            reason: '初次登场'
          }
        ]
      }];
    })(),
    _wasIncomplete: wasIncomplete // 标记是否进行了补全
  };

  return validated;
}

// 智能体类型
type AgentType = 'default' | 'agent_a' | 'agent_b' | 'agent_c';

// OpenAI 客户端缓存
const openaiClients: Record<AgentType, OpenAI | null> = {
  default: null,
  agent_a: null,
  agent_b: null,
  agent_c: null
};

// 消息计数器
const messageCounters: Record<string, number> = {};

/**
 * 获取游戏世界信息
 */
async function getGameWorldInfo(gameId: string): Promise<string> {
  try {
    // 读取游戏元数据文件
    const gameMetaPath = path.join(db.getGameDataDir(), gameId, 'game_meta.json');
    const gameMetaData = await fs.readFile(gameMetaPath, 'utf-8');
    const gameMeta = JSON.parse(gameMetaData);

    if (!gameMeta) {
      return '游戏世界信息暂不可用';
    }

    // 构建游戏世界信息字符串
    const worldInfo = `# 游戏世界信息

## 世界概览
- **世界名称**: ${gameMeta.overview?.worldName || '未知世界'}
- **类型**: ${gameMeta.overview?.genre || '未知类型'}
- **设定**: ${gameMeta.overview?.setting || '未知设定'}
- **基调**: ${gameMeta.overview?.tone || '未知基调'}
- **描述**: ${gameMeta.overview?.description || '暂无描述'}
- **背景**: ${gameMeta.overview?.background || '暂无背景'}

## 世界构建
${gameMeta.worldBuilding ? `
### 地点
${gameMeta.worldBuilding.locations?.map(loc =>
  `- **${loc.name}**: ${loc.description} (${loc.significance})`
).join('\n') || '暂无地点信息'}

### 势力
${gameMeta.worldBuilding.factions?.map(faction =>
  `- **${faction.name}**: ${faction.description} (${faction.relationship})`
).join('\n') || '暂无势力信息'}

### 魔法/能力体系
${gameMeta.worldBuilding.magicSystem ? `
- **名称**: ${gameMeta.worldBuilding.magicSystem.name}
- **描述**: ${gameMeta.worldBuilding.magicSystem.description}
- **等级**: ${gameMeta.worldBuilding.magicSystem.levels?.join(' → ') || '暂无等级信息'}
` : '暂无魔法体系信息'}
` : '暂无世界构建信息'}

## 游戏机制
${gameMeta.gameplayMechanics ? `
- **核心循环**: ${gameMeta.gameplayMechanics.coreLoop || '暂无'}
- **进展系统**: ${gameMeta.gameplayMechanics.progressionSystem || '暂无'}
- **冲突类型**: ${gameMeta.gameplayMechanics.conflictTypes?.join(', ') || '暂无'}
` : '暂无游戏机制信息'}

## 叙事结构
${gameMeta.narrativeStructure ? `
- **主题**: ${gameMeta.narrativeStructure.themes?.join(', ') || '暂无'}
- **情节钩子**: ${gameMeta.narrativeStructure.plotHooks?.join(', ') || '暂无'}
` : '暂无叙事结构信息'}

## 主要角色
${gameMeta.characters?.map(char =>
  `- **${char.name}** (${char.role}): ${char.description}`
).join('\n') || '暂无角色信息'}
`;

    return worldInfo;
  } catch (error) {
    console.warn('[AIService] 获取游戏世界信息失败:', error);
    return '游戏世界信息获取失败，请使用通用设定';
  }
}

/**
 * 生成章节分析信息，帮助Agent A做出章节决策
 */
async function generateChapterAnalysis(currentState: GameSaveState): Promise<string> {
  try {
    // 获取当前小说数据
    const novelData = await dataAccess.gameDataManager.getNovel(currentState.meta.gameId, currentState.meta.sessionId);
    const currentChapter = currentState.novel?.currentChapter || 'chapter-1';
    const chapters = novelData.chapters || [];

    // 找到当前章节
    const currentChapterData = chapters.find(c => c.id === currentChapter);
    const currentChapterIndex = chapters.findIndex(c => c.id === currentChapter);
    const totalChapters = chapters.length;

    // 分析当前章节的内容量
    const currentSections = currentChapterData?.sections || [];
    const sectionCount = currentSections.length;
    const totalWords = currentSections.reduce((sum, section) => sum + (section.text?.length || 0), 0);

    // 生成下一章节ID
    const nextChapterNumber = currentChapterIndex + 2; // +1 for 0-based index, +1 for next chapter
    const nextChapterId = `chapter-${nextChapterNumber}`;

    // 分析最近的故事发展
    const recentSections = currentSections.slice(-3); // 最近3个小节
    const recentContent = recentSections.map(s => s.text || '').join(' ').substring(0, 500);

    return `
## 📊 章节状态分析（供Agent A参考）

### 当前章节状态：
- 当前章节：${currentChapter}
- 章节标题：${currentChapterData?.chapter_title || '未知'}
- 已有小节数：${sectionCount}
- 章节字数：${totalWords}
- 总章节数：${totalChapters}

### 建议的下一章节ID：
- 如需新章节：${nextChapterId}

### 章节决策建议：
${sectionCount >= 5 ? '⚠️ 当前章节已有较多内容，考虑是否需要新章节' : '✅ 当前章节内容适中，可继续或新建'}
${totalWords >= 2000 ? '⚠️ 当前章节字数较多，适合转入新章节' : '✅ 当前章节字数适中'}

### 最近故事发展：
${recentContent || '暂无最近内容'}

**决策提示**：根据玩家行动的性质和上述分析，判断是继续当前章节还是创建新章节。
    `.trim();
  } catch (error) {
    console.error('[AIService] 生成章节分析失败:', error);
    return `
## 📊 章节状态分析

当前章节：${currentState.novel?.currentChapter || 'chapter-1'}
建议：根据故事发展需要决定是否创建新章节
    `.trim();
  }
}

/**
 * 构建Agent A的提示词
 */
async function buildAgentAPrompt(currentState: GameSaveState, userInput: string): Promise<{ system: string, user: string }> {
  // 加载系统级别的基础提示词
  const systemBasePrompt = await loadPromptTemplate('system_base');

  // 加载Agent A的专业提示词模板
  const agentAPromptTemplate = await loadPromptTemplate('agent_a_prompt');

  // 获取游戏世界信息
  const gameWorldInfo = await getGameWorldInfo(currentState.meta.gameId);

  // 生成章节分析信息
  const chapterAnalysis = await generateChapterAnalysis(currentState);

  // 获取聊天历史（用于上下文记忆）
  const chatHistory = await getChatHistory(currentState.meta.gameId, currentState.meta.sessionId);
  console.log('[DEBUG] buildAgentAPrompt - 聊天历史长度:', chatHistory.length);
  console.log('[DEBUG] buildAgentAPrompt - 聊天历史内容:', chatHistory.substring(0, 500) + '...');

  // 替换模板中的占位符
  const agentAPrompt = agentAPromptTemplate
    .replace('{{gameWorldInfo}}', gameWorldInfo)
    .replace('{{currentState}}', JSON.stringify(currentState, null, 2))
    .replace('{{userPrompt}}', userInput)
    .replace('{{chatHistory}}', chatHistory);

  // 组合系统基础提示词和Agent A专业提示词
  const combinedSystemPrompt = `${systemBasePrompt}\n\n${agentAPrompt}`;

  // 在系统提示词末尾添加章节分析
  const enhancedSystemPrompt = combinedSystemPrompt + '\n\n' + chapterAnalysis;

  console.log('[DEBUG] buildAgentAPrompt - 最终系统提示词长度:', enhancedSystemPrompt.length);
  console.log('[DEBUG] buildAgentAPrompt - 系统提示词包含聊天历史:', enhancedSystemPrompt.includes('## 聊天历史'));
  console.log('[DEBUG] buildAgentAPrompt - 系统提示词前1000字符:', enhancedSystemPrompt.substring(0, 1000));

  return {
    system: enhancedSystemPrompt,
    user: userInput
  };
}

/**
 * 获取聊天历史用于上下文记忆
 * 策略：最近50条消息直接发送，更早的消息通过总结提供
 */
async function getChatHistory(gameId: string, sessionId: string): Promise<string> {
  try {
    console.log('[DEBUG] getChatHistory - 开始获取聊天历史:', { gameId, sessionId });
    const chatData = await fileSystemService.readChatData(gameId, sessionId);
    console.log('[DEBUG] getChatHistory - 原始聊天数据:', chatData ? '存在' : '不存在');

    if (!chatData || !chatData.system || !Array.isArray(chatData.system)) {
      console.log('[DEBUG] getChatHistory - 没有聊天数据或格式不正确');
      return '## 聊天历史\n暂无聊天记录';
    }

    // 过滤出用户和系统的聊天消息
    console.log('[DEBUG] getChatHistory - 原始消息数量:', chatData.system.length);
    const allMessages = chatData.system
      .filter((msg: any) => msg.sender === 'user' || msg.sender === 'system')
      .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    console.log('[DEBUG] getChatHistory - 过滤后消息数量:', allMessages.length);
    if (allMessages.length === 0) {
      console.log('[DEBUG] getChatHistory - 没有有效的聊天消息');
      return '## 聊天历史\n暂无聊天记录';
    }

    let historyContent = '## 聊天历史\n';

    // 如果消息总数超过50条，需要总结早期消息
    if (allMessages.length > 50) {
      const earlyMessages = allMessages.slice(0, -50);
      const recentMessages = allMessages.slice(-50);

      // 简化的早期消息总结
      const earlySummary = `早期对话包含 ${earlyMessages.length} 条消息，主要涉及游戏规则讨论、角色互动等内容。`;

      historyContent += `### 早期对话总结\n${earlySummary}\n\n`;
      historyContent += `### 最近对话记录（最新50条）\n`;
      historyContent += formatMessages(recentMessages);
    } else {
      // 消息数量不超过50条，直接显示所有消息
      historyContent += `### 对话记录（共${allMessages.length}条）\n`;
      historyContent += formatMessages(allMessages);
    }

    return historyContent;
  } catch (error) {
    console.error('[AIService] 获取聊天历史失败:', error);
    return '## 聊天历史\n暂无聊天记录';
  }
}

/**
 * 格式化消息列表
 */
function formatMessages(messages: any[]): string {
  return messages
    .map((msg: any) => {
      const timestamp = new Date(msg.timestamp).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });

      if (msg.sender === 'user') {
        return `[${timestamp}] 用户: ${msg.content?.ask || msg.content || ''}`;
      } else if (msg.sender === 'system') {
        return `[${timestamp}] 小柔: ${msg.content?.answer || msg.content || ''}`;
      }
      return '';
    })
    .filter(line => line.length > 0)
    .join('\n');
}

/**
 * 构建Agent C的提示词
 */
async function buildAgentCPrompt(currentState: GameSaveState, agentAOutput: string, userInput: string): Promise<{ system: string, user: string }> {
  // 加载系统级别的基础提示词
  const systemBasePrompt = await loadPromptTemplate('system_base');

  // 加载Agent C的专业提示词模板
  const agentCPromptTemplate = await loadPromptTemplate('agent_c_prompt');

  // 获取游戏世界信息
  const gameWorldInfo = await getGameWorldInfo(currentState.meta.gameId);

  // 获取当前章节的完整内容
  let chapterContent = '';
  try {
    const novelData = await dataAccess.gameDataManager.getNovel(currentState.meta.gameId, currentState.meta.sessionId);
    if (novelData && novelData.chapters && novelData.chapters.length > 0) {
      const currentChapter = novelData.chapters.find(chapter =>
        chapter.id === currentState.novel?.currentChapter ||
        chapter.chapter === currentState.novel?.currentChapter
      );

      if (currentChapter && currentChapter.sections) {
        chapterContent = currentChapter.sections.map(section =>
          `### ${section.title}\n${section.text}`
        ).join('\n\n');
      }
    }
  } catch (error) {
    console.warn('[AIService] 获取章节内容失败，使用空内容:', error);
    chapterContent = '暂无章节内容';
  }

  // 替换模板中的占位符
  const agentCPrompt = agentCPromptTemplate
    .replace('{{gameWorldInfo}}', gameWorldInfo)
    .replace('{{agentAOutput}}', agentAOutput)
    .replace('{{currentState}}', JSON.stringify(currentState, null, 2))
    .replace('{{userInput}}', userInput);

  // 组合系统基础提示词和Agent C专业提示词
  const combinedSystemPrompt = `${systemBasePrompt}\n\n${agentCPrompt}`;

  return {
    system: combinedSystemPrompt,
    user: `请分析以下内容并输出JSON格式结果。`
  };
}

/**
 * 获取模型名称
 */
async function getModelName(agentType: AgentType = 'default'): Promise<string> {
  try {
    // 读取配置
    const config = await readLegacyConfig();

    // 检查配置是否有效
    if (!config || !config.openai) {
      console.error('[AIService] 无效的配置，使用默认模型');
      return 'gpt-4o';
    }

    // 获取对应智能体的模型名称
    const modelName = config.openai[agentType]?.model || config.openai.default?.model || 'gpt-4o';
    console.log(`[AIService] 使用模型: ${modelName} (${agentType})`);

    return modelName;
  } catch (error) {
    console.error(`[AIService] 获取模型名称失败:`, error);
    return 'gpt-4o'; // 默认模型
  }
}

/**
 * 获取Agent配置参数
 */
async function getAgentConfig(agentType: 'agent_a' | 'agent_b' | 'agent_c'): Promise<{
  temperature: number;
  maxTokens: number;
  topP?: number;
}> {
  try {
    // 使用新的配置系统
    const { readConfig } = await import('./config.service.js');
    const config = await readConfig();

    // 检查是否有专用的Agent配置
    if (config.agents && !config.agents.useDefault && config.agents[agentType]) {
      const agentConfig = config.agents[agentType];
      console.log(`[AIService] 使用Agent ${agentType}专用配置: maxTokens=${agentConfig.maxTokens}, temperature=${agentConfig.temperature}`);
      return {
        temperature: agentConfig.temperature || 0.7,
        maxTokens: agentConfig.maxTokens || 4096,
        topP: agentConfig.topP
      };
    }

    // 使用默认配置的降级逻辑
    const defaultProvider = config.llm.default;
    const defaultConfig = config.llm.providers[defaultProvider];
    console.log(`[AIService] 使用默认配置 for Agent ${agentType}`);
    return {
      temperature: defaultConfig?.temperature || 0.7,
      maxTokens: defaultConfig?.maxTokens || 4096,
      topP: defaultConfig?.topP
    };
  } catch (error) {
    console.error(`[AIService] 获取Agent配置失败:`, error);
    // 返回硬编码的默认值
    const defaults = {
      agent_a: { temperature: 0.7, maxTokens: 16384 },
      agent_b: { temperature: 0.8, maxTokens: 4096 },
      agent_c: { temperature: 0.5, maxTokens: 32768 }
    };
    console.log(`[AIService] 使用硬编码默认值 for Agent ${agentType}`);
    return defaults[agentType] || { temperature: 0.7, maxTokens: 4096 };
  }
}

/**
 * 初始化 OpenAI 客户端
 */
async function getOpenAIClient(agentType: AgentType = 'default'): Promise<OpenAI | null> {
  // 如果已经初始化，直接返回
  if (openaiClients[agentType]) {
    return openaiClients[agentType];
  }
  
  try {
    // 读取配置
    const config = await readLegacyConfig();
    
    // 检查配置是否有效
    if (!config || !config.openai) {
      console.error('[AIService] 无效的配置');
      return null;
    }
    
    // 获取对应智能体的API密钥
    const apiKey = config.openai[agentType]?.apiKey || config.openai.default?.apiKey;
    const baseUrl = config.openai[agentType]?.baseUrl || config.openai.default?.baseUrl;
    
    if (!apiKey || apiKey.trim() === '') {
      console.error(`[AIService] ${agentType}的API密钥未配置`);
      return null;
    }
    
    // 移除baseUrl中的前导和尾随空格
    const cleanBaseUrl = baseUrl ? baseUrl.trim() : undefined;
    
    console.log(`[AIService] 初始化 ${agentType} OpenAI 客户端，baseUrl: ${cleanBaseUrl}`);
    
    // 创建新的OpenAI客户端
    openaiClients[agentType] = new OpenAI({
      apiKey: apiKey,
      baseURL: cleanBaseUrl
    });
    
    return openaiClients[agentType];
  } catch (error) {
    console.error(`[AIService] 初始化${agentType}的OpenAI客户端失败:`, error);
    return null;
  }
}

/**
 * 生成有序的消息ID
 * @param prefix 前缀，如'user'或'ai_assistant'
 * @param gameId 游戏ID
 * @param sessionId 会话ID
 * @returns 格式化的消息ID
 */
async function generateSequentialId(prefix: string, gameId: string, sessionId: string): Promise<string> {
  // 使用标准化的消息ID格式
  return generateStandardMessageId(prefix);
}

/**
 * 自动检测Agent A输出中的新角色
 */
function detectNewCharactersFromNarrative(narrative: string, existingCharacters: any[]): any[] {
  const newCharacters: any[] = [];

  // 常见的角色名称模式
  const characterPatterns = [
    // 匹配"王长老"、"张师兄"等完整称呼
    /([王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾肖田董袁潘于蒋蔡余杜叶程魏苏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤])(师兄|师姐|师弟|师妹|长老|掌门|弟子|道友|公子|小姐)(?![的地得])/g,
    // 匹配"林婉儿"等完整姓名 - 更灵活的两字名匹配
    /([王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾肖田董袁潘于蒋蔡余杜叶程魏苏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤])([一-龯]{1,2})(?![的地得师兄姐弟妹长老掌门弟子道友公子小姐])/g,
    // 匹配引号中的称呼
    /"([^"]{2,8}(?:师兄|师姐|师弟|师妹|长老|掌门|弟子|道友|公子|小姐))"/g,
    // 匹配"俗称的"后面的称呼
    /俗称的"([^"]{2,8})"/g,
    // 匹配"叫"后面的名字 - 支持"他叫"、"她叫"等
    /[他她它们]?叫做?([王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾肖田董袁潘于蒋蔡余杜叶程魏苏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤][一-龯]{1,2})(?![的地得])/g,
    // 匹配"我是XXX"格式
    /我是([王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾肖田董袁潘于蒋蔡余杜叶程魏苏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤][一-龯]{1,2})(?![的地得])/g
  ];

  const detectedNames = new Set<string>();

  // 使用正则表达式检测角色名称
  characterPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(narrative)) !== null) {
      const fullName = match[1] + (match[2] || '');
      if (fullName.length >= 2 && fullName.length <= 8) {
        detectedNames.add(fullName);
      }
    }
  });

  // 检查哪些是新角色
  const existingNames = existingCharacters.map(char => char.name);

  detectedNames.forEach(name => {
    if (!existingNames.includes(name) && name !== '李明') {
      // 生成角色ID - 使用拼音转换
      let charId = '';
      if (name === '王长老') charId = 'char_wangzhanglao';
      else if (name === '张师兄') charId = 'char_zhangshixiong';
      else if (name === '赵师姐') charId = 'char_zhaoshijie';
      else if (name === '林婉儿') charId = 'char_linwaner';
      else if (name === '李飞宇') charId = 'char_lifeiyu';
      else {
        // 默认处理：移除称呼后缀，转换为拼音
        const baseName = name.replace(/(师兄|师姐|师弟|师妹|长老|掌门|弟子|道友|公子|小姐)$/g, '');
        // 简单的拼音映射
        const pinyinMap: { [key: string]: string } = {
          '王': 'wang', '李': 'li', '张': 'zhang', '刘': 'liu', '陈': 'chen', '杨': 'yang',
          '飞': 'fei', '宇': 'yu', '明': 'ming', '华': 'hua', '强': 'qiang', '军': 'jun',
          '婉': 'wan', '儿': 'er', '小': 'xiao', '雨': 'yu', '梦': 'meng', '瑶': 'yao'
        };
        let pinyin = '';
        for (const char of baseName) {
          pinyin += pinyinMap[char] || char.toLowerCase();
        }
        charId = `char_${pinyin}`;
      }

      // 推断角色属性
      const isElder = name.includes('长老') || name.includes('掌门');
      const isSenior = name.includes('师兄') || name.includes('师姐');
      const isJunior = name.includes('师弟') || name.includes('师妹');

      const newCharacter = {
        id: charId,
        name: name,
        description: generateCharacterDescription(name, narrative),
        avatar: "",
        personality: generatePersonality(name),
        background: generateBackground(name),
        relationships: {
          player: generateRelationship(name)
        },
        attributes: generateAttributes(name, isElder, isSenior, isJunior),
        isPresent: true, // 新出现的角色通常在场
        isPlayer: false,
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      };

      newCharacters.push(newCharacter);
      console.log(`[AIService] 🎭 自动检测到新角色: ${name} (ID: ${charId})`);
    }
  });

  return newCharacters;
}

/**
 * 生成角色描述
 */
function generateCharacterDescription(name: string, narrative: string): string {
  // 从叙述中提取角色描述
  const sentences = narrative.split(/[。！？]/).filter(s => s.includes(name));
  if (sentences.length > 0) {
    return sentences[0].trim() + '。';
  }

  if (name.includes('长老')) return '宗门内的长老级人物，修为深厚，地位崇高。';
  if (name.includes('师兄')) return '宗门内的资深弟子，修为较高，经验丰富。';
  if (name.includes('师姐')) return '宗门内的资深女弟子，修为不俗，温柔贤淑。';
  if (name.includes('师弟') || name.includes('师妹')) return '宗门内的新入门弟子，修为尚浅，但潜力不错。';

  return '宗门内的修炼者，身份神秘。';
}

/**
 * 生成角色性格
 */
function generatePersonality(name: string): string {
  if (name.includes('长老')) return '威严庄重，深不可测';
  if (name.includes('师兄')) return '沉稳可靠，乐于助人';
  if (name.includes('师姐')) return '温柔贤淑，善解人意';
  if (name.includes('师弟')) return '勤奋好学，谦逊有礼';
  if (name.includes('师妹')) return '活泼可爱，天真烂漫';

  return '性格待观察';
}

/**
 * 生成角色背景
 */
function generateBackground(name: string): string {
  if (name.includes('长老')) return '宗门内的元老级人物，修炼多年，见多识广。';
  if (name.includes('师兄') || name.includes('师姐')) return '宗门内的资深弟子，已修炼数年，经验丰富。';
  if (name.includes('师弟') || name.includes('师妹')) return '新入门的弟子，刚刚开始修仙之路。';

  return '身份背景待了解';
}

/**
 * 生成角色关系
 */
function generateRelationship(name: string): string {
  if (name.includes('长老')) return '长老与弟子关系';
  if (name.includes('师兄') || name.includes('师姐')) return '师兄妹关系';
  if (name.includes('师弟') || name.includes('师妹')) return '师兄妹关系';

  return '同门关系';
}

/**
 * 生成角色属性
 */
function generateAttributes(name: string, isElder: boolean, isSenior: boolean, isJunior: boolean): any {
  if (isElder) {
    return {
      境界: '金丹期',
      功法: '宗门高级功法',
      体质: '特殊体质',
      灵根: '上品灵根'
    };
  } else if (isSenior) {
    return {
      境界: '炼气五层',
      功法: '宗门基础功法',
      体质: '普通',
      灵根: '中品灵根'
    };
  } else {
    return {
      境界: '炼气一层',
      功法: '《基础吐纳术》（入门）',
      体质: '普通',
      灵根: '下品灵根'
    };
  }
}

/**
 * 生成有意义的章节标题
 */
function generateChapterTitle(chapterId: string, gameState: any): string {
  // 从章节ID中提取章节号
  const chapterMatch = chapterId.match(/chapter-(\d+)/);
  const chapterNumber = chapterMatch ? parseInt(chapterMatch[1]) : 1;

  // 根据游戏状态和章节号生成有意义的标题
  const location = gameState?.novel?.location || gameState?.world?.location || '';
  const currentSection = gameState?.novel?.currentSection || '';

  // 生成标题的逻辑
  if (chapterNumber === 1) {
    if (location) {
      return `第一章：初入${location}`;
    } else if (currentSection && currentSection !== '当前场景') {
      return `第一章：${currentSection}`;
    } else {
      return '第一章：修仙之路的开始';
    }
  } else {
    // 为后续章节生成标题
    const titles = [
      '修为突破',
      '奇遇降临',
      '危机四伏',
      '师门试炼',
      '秘境探索',
      '强敌来袭',
      '机缘巧合',
      '境界提升'
    ];
    const titleIndex = (chapterNumber - 2) % titles.length;
    return `第${chapterNumber}章：${titles[titleIndex]}`;
  }
}

// 读取提示词模板
async function loadPromptTemplate(templateName: string): Promise<string> {
  try {
    // 使用db.getPromptTemplatePath获取正确的模板路径
    const templatePath = db.getPromptTemplatePath(templateName);
    console.log(`[AIService] 加载提示词模板: ${templateName}, 路径: ${templatePath}`);
    
    try {
      const content = await fs.readFile(templatePath, 'utf-8');
      console.log(`[AIService] 成功加载提示词模板: ${templateName}`);
      return content;
    } catch (error) {
      console.error(`[AIService] 读取提示词模板失败: ${templateName}`, error);
      
      // 如果模板名称是agent_a_prompt，则转换为agent_a_prompt.md尝试
      if (templateName === 'agent_a') {
        console.log(`[AIService] 尝试加载agent_a_prompt.md`);
        return await fs.readFile(db.getPromptTemplatePath('agent_a_prompt'), 'utf-8');
      }
      
      throw error;
    }
  } catch (error) {
    console.error(`[ERROR] 加载提示词模板 ${templateName} 失败:`, error);
    
    // 根据模板名称返回默认内容
    console.log(`[AIService] 使用默认模板内容: ${templateName}`);
    
    if (templateName === 'system_base') {
      return '# 创世伙伴：AI链式专家系统\n\n你是创世伙伴系统中的一个专业化AI智能体，负责共同创建沉浸式的角色扮演游戏体验。';
    } else if (templateName === 'agent_a' || templateName === 'agent_a_prompt') {
      return '# Agent A: 创意者指令\n\n你是Agent A (创意者)，负责生成故事叙事和场景描述。你需要返回符合JSON格式的内容，包含narrative、sceneUpdate、suggestedOptions等字段。';
    } else if (templateName === 'agent_b' || templateName === 'agent_actor') {
      return '# Agent B: 表演者指令\n\n你是Agent B (表演者)，负责处理角色对话回复。';
    } else if (templateName === 'agent_c' || templateName === 'agent_analyst') {
      return '# Agent C: 分析者指令\n\n你是Agent C (分析者)，负责更新游戏状态和生成视觉提示。';
    } else {
      return `# ${templateName}\n\n默认提示词模板`;
    }
  }
}

// 导入共享类型和验证Schema
import {
  InteractionResponseSchema,
  GameStateSchema,
  DialogueMessageSchema
} from '../../../types/validation.js';
import { SystemError } from '../../../types/errors.js';

// ===== AI调用结果类型 =====

export interface AICallResult<T = any> {
  success: boolean;
  data?: T;
  error?: string | SystemError;
  metadata?: {
    model?: string;
    inputTokens?: number;
    outputTokens?: number;
    cost?: number;
    executionTime?: number;
    retryCount?: number;
  };
  retryCount?: number;
  executionTime?: number;
  choices?: any[];
}

import type {
  AgentAOutput,
  AgentBOutput,
  AgentCOutput,
  AIServiceConfig
} from '../../../shared/types/ai.js';

import { AgentAOutputSchema } from '../../../shared/types/ai.js';

// 使用共享的验证Schema替换本地定义
const interactionResponseSchema = InteractionResponseSchema;

/**
 * 生成模拟的Agent A输出
 */
function generateMockAgentAOutput(prompt: string): any {
  return {
    narrative: `这是对"${prompt}"的模拟叙事响应。由于未配置OpenAI API密钥，系统正在使用模拟数据。`,
    sceneUpdate: {
      name: "模拟场景",
      description: "这是一个模拟场景描述。请配置OpenAI API密钥以获取真实的AI生成内容。",
      visualPrompt: {
        description: "模拟视觉提示描述",
        style: "现代写实风格"
      },
      time: "黄昏时分",
      location: "模拟位置",
      environment: "平静的环境"
    },
    cgOpportunities: [
      {
        characterId: "mock_character_1",
        description: "模拟CG场景描述"
      }
    ],
    suggestedOptions: [
      "模拟选项1: 继续探索",
      "模拟选项2: 与角色对话",
      "模拟选项3: 返回上一场景"
    ],
    sectionTitle: `${prompt}` // 使用玩家指令作为小节标题
  };
}

/**
 * 生成模拟的InteractionResponse
 */
function generateMockInteractionResponse(currentState: GameSaveState, prompt: string, characterId?: string): InteractionResponse {
  const timestamp = new Date().toISOString();
  const messageId = uuidv4();
  
  // 复制当前状态作为更新后的状态
  const updatedGameState = JSON.parse(JSON.stringify(currentState)) as GameSaveState;
  
  // 更新最后保存时间
  updatedGameState.meta.lastSavedAt = timestamp;
  
  // 创建UI内容
  const formattedUIContent: FormattedDialogueMessage[] = [{
    id: messageId,
    sender: characterId ? 'ai' : 'system',
    content: {
      [characterId ? 'answer' : 'narrative']: characterId
        ? `这是对"${prompt}"的模拟角色回复。请配置OpenAI API密钥以获取真实的AI生成内容。`
        : `这是对"${prompt}"的模拟叙事响应。请配置OpenAI API密钥以获取真实的AI生成内容。`
    },
    timestamp,
    characterId,
    isNarrative: !characterId
  }];
  
  // 添加模拟的建议选项
  const suggestedOptions = [
    "模拟选项1: 继续探索",
    "模拟选项2: 与角色对话",
    "模拟选项3: 返回上一场景"
  ];
  
  return {
    updatedGameState,
    formattedUIContent,
    suggestedOptions,
    sectionTitle: prompt // 使用玩家指令作为小节标题
  };
}

/**
 * 统一的AI服务配置
 */
const AI_SERVICE_CONFIG: AIServiceConfig = {
  maxRetries: 3,
  timeoutMs: 60000,
  fallbackEnabled: true,
  retryDelayMs: 1000
};

/**
 * 创建系统错误对象
 */
function createSystemError(type: SystemError['type'], message: string, context?: Record<string, any>): SystemError {
  return {
    code: 'AI_SERVICE_ERROR',
    type,
    message,
    recoverable: type !== 'unknown',
    timestamp: Date.now(),
    context
  };
}

/**
 * 指数退避重试函数
 */
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = AI_SERVICE_CONFIG.maxRetries,
  baseDelay: number = AI_SERVICE_CONFIG.retryDelayMs
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 检查是否应该重试
      if (error.status === 429 || error.name === 'AbortError' || error.code === 'ECONNRESET') {
        const delay = baseDelay * Math.pow(2, attempt);
        console.log(`[AIService] 重试 ${attempt + 1}/${maxRetries}，等待 ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // 对于不可重试的错误，直接抛出
        throw error;
      }
    }
  }

  throw lastError!;
}

/**
 * 改进的AI调用函数 - 支持重试和错误处理
 * 兼容不同 OpenAI 客户端版本的聊天完成调用
 * 处理不同的API响应格式和错误情况
 */
async function callCompatibleChatCompletion(client: any, params: {
  model: string,
  messages: any[],
  temperature?: number,
  max_tokens?: number,
  response_format?: any
}): Promise<AICallResult<any>> {
  console.log(`[AIService] 调用聊天完成API，模型: ${params.model}`);

  if (!client) {
    return {
      success: false,
      error: 'AI客户端未初始化',
      retryCount: 0,
      executionTime: 0
    };
  }

  const startTime = Date.now();
  let retryCount = 0;

  try {
    const result = await retryWithBackoff(async () => {
      retryCount++;

      // 设置超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), AI_SERVICE_CONFIG.timeoutMs);

      try {
        // 调用API
        const response = await client.chat.completions.create({
          ...params
        }, { signal: controller.signal });

        // 清除超时
        clearTimeout(timeoutId);

        // 验证响应
        if (!response.choices?.[0]?.message?.content) {
          throw new Error('AI返回空响应');
        }

        return {
          choices: [
            {
              message: {
                content: response.choices[0].message.content,
                role: response.choices[0].message.role || 'assistant'
              }
            }
          ]
        };
      } catch (error: any) {
        clearTimeout(timeoutId);
        throw error;
      }
    });

    const executionTime = Date.now() - startTime;
    console.log(`[AIService] API调用成功，耗时: ${executionTime}ms，重试次数: ${retryCount - 1}`);

    return {
      success: true,
      data: result,
      retryCount: retryCount - 1,
      executionTime
    };

  } catch (error: any) {
    const executionTime = Date.now() - startTime;
    let errorMessage = 'AI调用失败';

    if (error.name === 'AbortError') {
      errorMessage = 'AI调用超时';
    } else if (error.status === 429) {
      errorMessage = 'AI服务速率限制';
    } else if (error.message) {
      errorMessage = error.message;
    }

    console.error(`[AIService] ${errorMessage}，耗时: ${executionTime}ms，重试次数: ${retryCount - 1}`);

    return {
      success: false,
      error: errorMessage,
      retryCount: retryCount - 1,
      executionTime
    };
  }
}

// 确保游戏状态包含所有必要字段
function ensureCompleteGameState(
  returnedState: Partial<GameSaveState>,
  currentState: GameSaveState,
  newEvent?: GameEvent
): GameSaveState {
  console.log('[AIService] 确保游戏状态完整性');
  
  const completeState: GameSaveState = {
    ...JSON.parse(JSON.stringify(currentState)),
    ...returnedState
  };
  
  // 确保events数组存在
  if (!completeState.novel.events) {
    console.log('[AIService] 缺少events数组，初始化为空数组');
    completeState.novel.events = [];
  }
  
  // 修复事件格式，严格使用 GameEvent 结构
  completeState.novel.events = completeState.novel.events.map(event => {
    return {
      id: event.id || uuidv4(),
      description: (event as any).description || (event as any).content || '',
      timestamp: event.timestamp || new Date().toISOString()
    };
  });
  
  // 防止重复事件
  let shouldAddNewEvent = true;
  if (newEvent) {
    shouldAddNewEvent = !completeState.novel.events.some(e =>
      e.id === newEvent.id ||
      (e.description && newEvent.description &&
        (e.description.includes(newEvent.description) || newEvent.description.includes(e.description)))
    );
  }
  
  if (newEvent && shouldAddNewEvent) {
    console.log('[AIService] 添加新事件到events数组');
    completeState.novel.events.push(newEvent);
  }
  
  // characters、meta 保持不变（interactionLog已废弃）
  
  if (!completeState.characters) {
    console.log('[AIService] 缺少characters数组，使用当前状态的characters');
    completeState.characters = currentState.characters || [];
  }
  
  if (!completeState.meta) {
    console.log('[AIService] 缺少meta对象，使用当前状态的meta');
    completeState.meta = currentState.meta;
  }
  
  return completeState;
}



/**
 * Agent A 处理流程
 * @param currentState 当前游戏状态
 * @param userInput 玩家输入
 * @param sectionId 可选，指定小节ID用于重写
 */
async function agentAProcess(currentState: GameSaveState, userInput: string, sectionId?: string): Promise<AgentAOutput> {
  const agentAClient = await getOpenAIClient('agent_a');
  if (!agentAClient) throw new Error('无法初始化Agent A的OpenAI客户端');

  const agentAPrompt = await buildAgentAPrompt(currentState, userInput);

  // 获取Agent A的配置参数
  const agentConfig = await getAgentConfig('agent_a');

  // 使用改进的AI调用函数
  const aiResult = await callCompatibleChatCompletion(agentAClient, {
    model: await getModelName('agent_a'),
    messages: [
      { role: 'system', content: agentAPrompt.system },
      { role: 'user', content: agentAPrompt.user }
    ],
    temperature: agentConfig.temperature,
    max_tokens: agentConfig.maxTokens,
    response_format: { type: "json_object" }
  });

  if (!aiResult.success) {
    throw new Error(`Agent A调用失败: ${aiResult.error}`);
  }

  const agentAOutput = aiResult.data?.choices[0]?.message?.content || '';
  if (!agentAOutput) throw new Error('Agent A返回的内容为空');

  let agentAData;
  try {
    agentAData = JSON.parse(agentAOutput);
  } catch (jsonError) {
    console.error('[AIService] Agent A JSON解析失败，原始输出:', agentAOutput);
    throw new Error('[AIService] Agent A输出不是有效的JSON格式');
  }

  // 使用zod schema严格校验
  const parseResult = AgentAOutputSchema.safeParse(agentAData);
  if (!parseResult.success) {
    console.error('[AIService] Agent A输出验证失败:', parseResult.error.issues);
    console.error('[AIService] 原始数据:', agentAData);
    throw new Error('[AIService] Agent A输出不符合schema: ' + JSON.stringify(parseResult.error.issues));
  }

  console.log(`[AIService] Agent A处理成功，重试次数: ${aiResult.retryCount}，耗时: ${aiResult.executionTime}ms`);

  // 获取玩家姓名用于变量替换
  const playerName = currentState.meta?.playerName || '玩家';

  // 对生成的内容进行变量替换
  const replaceVariables = (text: string): string => {
    if (!text) return text;
    return text.replace(/\{\{user\}\}/g, playerName);
  };

  // 返回标准的AgentAOutput格式，并替换所有变量
  return {
    mode: parseResult.data.mode || 'create',
    narrative: replaceVariables(parseResult.data.narrative),
    suggestedOptions: parseResult.data.suggestedOptions?.map(option => replaceVariables(option)) || [],
    sectionTitle: replaceVariables(parseResult.data.sectionTitle),
    sectionsId: parseResult.data.sectionsId,
    answer: replaceVariables(parseResult.data.answer || '好的，我已经依照主人的安排创作了剧情~'),
    chaptersInfo: parseResult.data.chaptersInfo ? {
      id: parseResult.data.chaptersInfo.id || 'chapter-1',
      chapterTitle: replaceVariables(parseResult.data.chaptersInfo.chapterTitle || '默认章节'),
      time: replaceVariables(parseResult.data.chaptersInfo.time || ''),
      location: replaceVariables(parseResult.data.chaptersInfo.location || ''),
      environment: replaceVariables(parseResult.data.chaptersInfo.environment || '')
    } : {
      id: 'chapter-1',
      chapterTitle: '默认章节',
      time: '',
      location: '',
      environment: ''
    }
  };
}

// 2. AgentB处理流程
async function agentBProcess(currentState: GameSaveState, userInput: { prompt: string; characterId: string }): Promise<{ agentBOutput: string; targetCharacter: any }> {
  const agentBClient = await getOpenAIClient('agent_b');
  if (!agentBClient) throw new Error('无法初始化Agent B的OpenAI客户端');

  // 获取游戏世界信息
  const gameWorldInfo = await getGameWorldInfo(currentState.meta.gameId);

  const systemBasePrompt = await loadPromptTemplate('system_base');
  let agentBPromptTemplate;
  try {
    agentBPromptTemplate = await loadPromptTemplate('agent_b_prompt');
  } catch (error) {
    agentBPromptTemplate = await loadPromptTemplate('agent_actor');
  }

  const targetCharacter = currentState.characters.find(char => char.id === userInput.characterId);
  if (!targetCharacter) throw new Error(`找不到ID为${userInput.characterId}的角色`);

  // 替换模板中的占位符
  const agentBPrompt = agentBPromptTemplate
    .replace('{{gameWorldInfo}}', gameWorldInfo)
    .replace('{{currentState}}', JSON.stringify(currentState, null, 2))
    .replace('{{targetCharacter}}', JSON.stringify(targetCharacter, null, 2))
    .replace('{{playerMessage}}', userInput.prompt);

  // 构建上下文
  const agentBContext = `${systemBasePrompt}\n\n${agentBPrompt}`;

  // 获取Agent B的配置参数
  const agentConfig = await getAgentConfig('agent_b');

  // 使用改进的AI调用函数
  const aiResult = await callCompatibleChatCompletion(agentBClient, {
    model: await getModelName('agent_b'),
    messages: [
      { role: "system", content: agentBContext },
      { role: "user", content: userInput.prompt }
    ],
    temperature: agentConfig.temperature,
    max_tokens: agentConfig.maxTokens,
    response_format: { type: "text" }
  });

  if (!aiResult.success) {
    throw new Error(`Agent B调用失败: ${aiResult.error}`);
  }

  let agentBOutput = aiResult.data?.choices[0]?.message?.content;
  if (!agentBOutput) throw new Error('Agent B返回的内容为空');

  // 获取玩家姓名用于变量替换
  const playerName = currentState.meta?.playerName || '玩家';

  // 对Agent B的输出进行变量替换
  agentBOutput = agentBOutput.replace(/\{\{user\}\}/g, playerName);

  console.log(`[AIService] Agent B处理成功，重试次数: ${aiResult.retryCount}，耗时: ${aiResult.executionTime}ms`);

  return { agentBOutput, targetCharacter };
}

/**
 * 将结构化的世界说明转换为AI可读的文本格式
 */
function formatWorldSystemGuideForAI(worldSystemGuide: any): string {
  let text = '';

  if (worldSystemGuide.attributeSystem) {
    text += `## 属性系统\n`;
    text += `${worldSystemGuide.attributeSystem.description}\n\n`;

    if (worldSystemGuide.attributeSystem.attributes) {
      text += '### 属性详情:\n';
      worldSystemGuide.attributeSystem.attributes.forEach((attr: any) => {
        text += `- **${attr.name}**: ${attr.description}\n`;
        text += `  - 变化规则: ${attr.changeRules}\n`;
        if (attr.valueRange) text += `  - 数值范围: ${attr.valueRange}\n`;
        text += `  - 关联影响: ${attr.interactions}\n\n`;
      });
    }
  }

  if (worldSystemGuide.coreMechanics) {
    text += `## 核心机制\n`;
    text += `${worldSystemGuide.coreMechanics.description}\n\n`;
    text += `- **游戏循环**: ${worldSystemGuide.coreMechanics.gameLoop}\n`;
    text += `- **冲突解决**: ${worldSystemGuide.coreMechanics.conflictResolution}\n`;
    text += `- **发展系统**: ${worldSystemGuide.coreMechanics.progressionSystem}\n`;
    text += `- **资源系统**: ${worldSystemGuide.coreMechanics.resourceSystem}\n\n`;
  }

  if (worldSystemGuide.aiGuidelines) {
    text += `## AI行为准则\n\n`;

    if (worldSystemGuide.aiGuidelines.shouldDo) {
      text += `### AI应该遵守的规则\n`;
      text += `${worldSystemGuide.aiGuidelines.shouldDo}\n\n`;
    }

    if (worldSystemGuide.aiGuidelines.shouldNotDo) {
      text += `### AI禁止的行为\n`;
      text += `${worldSystemGuide.aiGuidelines.shouldNotDo}\n\n`;
    }

    if (worldSystemGuide.aiGuidelines.customRules) {
      text += `### 自定义规则\n`;
      text += `${worldSystemGuide.aiGuidelines.customRules}\n\n`;
    }
  }

  return text;
}

/**
 * Agent C 处理流程
 * @param currentState 当前游戏状态
 * @param agentAOutput Agent A 的输出（JSON字符串）
 * @param userInput 玩家输入
 * @param worldSystemGuide 世界规则/属性系统说明
 */
async function agentCProcess(currentState: GameSaveState, agentAOutput: string, userInput: string, worldSystemGuide?: any) {
  const agentCClient = await getOpenAIClient('agent_c');
  if (!agentCClient) throw new Error('无法初始化Agent C的OpenAI客户端');

  // === 拼接 worldSystemGuide 到 system prompt ===
  let agentCPrompt = await buildAgentCPrompt(currentState, agentAOutput, userInput);
  if (worldSystemGuide) {
    // 将结构化的世界说明转换为文本格式
    const worldGuideText = formatWorldSystemGuideForAI(worldSystemGuide);
    agentCPrompt.system += `\n\n# 世界规则与属性系统\n${worldGuideText}`;
  }

  // 获取Agent C的配置参数
  const agentConfig = await getAgentConfig('agent_c');

  const agentCResponse = await agentCClient.chat.completions.create({
    model: await getModelName('agent_c'),
    messages: [
      { role: 'system', content: agentCPrompt.system },
      { role: 'user', content: agentCPrompt.user }
    ],
    temperature: agentConfig.temperature,
    max_tokens: agentConfig.maxTokens,
    response_format: { type: "json_object" }
  });

  const agentCOutput = agentCResponse.choices[0]?.message?.content || '';
  if (!agentCOutput) throw new Error('Agent C返回的内容为空');

  // 打印Agent C的原始输出，用于调试JSON解析错误
  console.log('[AIService] Agent C原始输出内容:', agentCOutput);

  let agentCData;
  try {
    agentCData = JSON.parse(agentCOutput);
    console.log('[Agent:agent_c] 解析后的JSON:', JSON.stringify(agentCData, null, 2));
  } catch (jsonError) {
    console.error('[AIService] Agent C输出解析JSON错误详情:', jsonError);
    console.error('[AIService] 无效的JSON内容:', agentCOutput);
    throw new Error('[AIService] Agent C输出不是有效的JSON格式');
  }

  // 验证Agent C输出的完整性
  if (!agentCData.updatedCharacters || !Array.isArray(agentCData.updatedCharacters)) {
    console.warn('[AIService] Agent C返回的updatedCharacters无效，设置为空数组');
    agentCData.updatedCharacters = [];
  }

  // 检查是否有新角色需要处理但被遗漏
  if (agentCData.updatedCharacters.length === 0) {
    console.warn('[AIService] ⚠️ Agent C返回空的updatedCharacters数组，这可能表示存在问题');
    console.warn('[AIService] 请检查Agent A输出中是否包含新角色信息');
  } else {
    console.log(`[AIService] ✅ Agent C返回了${agentCData.updatedCharacters.length}个角色更新`);
    agentCData.updatedCharacters.forEach((char: any, index: number) => {
      console.log(`[AIService] 角色${index + 1}: ${char.name || char.id} (在场: ${char.isPresent})`);
    });
  }

  // === 属性 reason 合并与角色属性变化记录 ===
  const characterChanges: { [characterId: string]: string[] } = {};
  if (agentCData && agentCData.characters && Array.isArray(agentCData.characters)) {
    agentCData.characters.forEach((updatedChar: any) => {
      const oldChar = currentState.characters.find(c => c.id === updatedChar.id);
      if (!oldChar) return;

      const changes: string[] = [];
      updatedChar.attributeCollections?.forEach((updatedCol: any) => {
        const oldCol = oldChar.attributeCollections.find((c: any) => c.id === updatedCol.id);
        if (!oldCol) return;
        updatedCol.fields?.forEach((updatedField: any) => {
          const oldField = oldCol.fields.find((f: any) => f.id === updatedField.id);
          if (!oldField) return;
          // reason 合并与去重
          if (updatedField.reason && oldField.reason) {
            if (updatedField.reason !== oldField.reason) {
              updatedField.reason = oldField.reason + '；' + updatedField.reason;
            }
          }
          // 记录属性变化
          if (updatedField.reason && updatedField.reason !== oldField.reason) {
            changes.push(`${updatedField.label}：${updatedField.reason}`);
          }
          // === isNew 归零 ===
          if (typeof updatedField.isNew === 'boolean') {
            updatedField.isNew = false;
          }
        });
      });

      if (changes.length > 0) {
        characterChanges[updatedChar.id] = changes;
      }
    });
  }

  // === 注释：角色属性变化处理已移至后续统一处理逻辑中，避免重复 ===
  // 补 answer 字段 - 使用更有意义的回复
  if (!agentCData.answer) {
    agentCData.answer = '好的，我已经依照主人的安排创作了剧情~';
  }
  return agentCData;
}

/**
 * 安全更新游戏状态
 * 当Agent C返回无效JSON或处理失败时，保留原始游戏状态
 */
function safeUpdateGameState(updatedState: any, currentState: GameSaveState): GameSaveState {
  if (!updatedState) {
    console.log('[AIService] 更新状态为空，返回当前状态');
    return currentState;
  }

  try {
    // 确保meta信息保留
    if (!updatedState.meta && currentState.meta) {
      updatedState.meta = { ...currentState.meta };
    }
    
    // 确保lastSavedAt更新
    if (updatedState.meta) {
      updatedState.meta.lastSavedAt = new Date().toISOString();
    }
    
    // 确保novel存在
    if (!updatedState.novel && currentState.novel) {
      updatedState.novel = { ...currentState.novel };
    }
    
    // 确保characters存在
    if (!updatedState.characters && currentState.characters) {
      updatedState.characters = [...currentState.characters];
    }
    
    // interactionLog已废弃，消息现在存储在聊天文件中
    
    return updatedState as GameSaveState;
  } catch (error) {
    console.error('[AIService] 安全更新游戏状态时出错:', error);
    return currentState;
  }
}

/**
 * 导演模式主流程 - 使用新的Agent框架和数据访问层
 * @param currentState 当前游戏状态
 * @param userInput { prompt: string, sectionId?: string, worldSystemGuide?: any }
 * @returns InteractionResponse
 */
export async function executeDirectorFlow(
  currentState: GameSaveState,
  userInput: { prompt: string; sectionId?: string; worldSystemGuide?: any }
): Promise<InteractionResponse> {
  const startTime = Date.now();

  try {
    console.log('[AIService] 开始执行导演模式流程，用户输入:', userInput.prompt);

    // 读取 game_meta.json 获取 characterAttributeSchema
    let characterAttributeSchema = null;
    try {
      const gameMetaPath = path.join(db.getGameDataDir(), currentState.meta.gameId, 'game_meta.json');
      const gameMetaContent = await fs.readFile(gameMetaPath, 'utf-8');
      const gameMeta = JSON.parse(gameMetaContent);
      characterAttributeSchema = gameMeta.characterAttributeSchema;
      console.log('[AIService] 导演模式成功读取 characterAttributeSchema，属性组数量:', characterAttributeSchema?.groups?.length || 0);
    } catch (error) {
      console.warn('[AIService] 导演模式无法读取 characterAttributeSchema:', error.message);
    }

    // 创建Agent上下文
    const context: AgentContext = {
      gameId: currentState.meta.gameId,
      sessionId: currentState.meta.sessionId,
      userInput: userInput.prompt,
      currentState: currentState,
      characterAttributeSchema: characterAttributeSchema, // 传递属性架构
      metadata: {
        sectionId: userInput.sectionId,
        worldSystemGuide: userInput.worldSystemGuide
      }
    };

    // 创建并执行Agent链
    const chain = await AgentChainFactory.createDirectorChain(context);
  const chainResult: ChainResult = await chain.execute();

  if (!chainResult.success) {
    throw new Error(`Agent链执行失败: ${chainResult.error}`);
  }

  // 提取Agent结果
  const agentAResult = chainResult.results[0]?.data;
  const agentCResult = chainResult.results[1]?.data;

  if (!agentAResult) {
    throw new Error('Agent A 未返回有效结果');
  }

  // 调试：检查 Agent A 的建议选项
  console.log('[DEBUG] Agent A 结果:', JSON.stringify(agentAResult, null, 2));
  console.log('[DEBUG] Agent A suggestedOptions:', agentAResult.suggestedOptions);

  console.log(`[AIService] Agent链执行完成，总耗时: ${chainResult.totalExecutionTime}ms`);
  console.log(`[AIService] agentAResult.mode:`, agentAResult.mode);

  // 检查是否为聊天模式
  if (agentAResult.mode === 'chat') {
    console.log('[AIService] 检测到聊天模式，使用简化处理流程');
    return await handleChatMode(currentState, userInput, agentAResult, startTime);
  }

  console.log('[AIService] 检测到创作模式，使用完整处理流程');
  console.log(`[AIService] agentAResult.chaptersInfo:`, JSON.stringify(agentAResult.chaptersInfo, null, 2));

  // 使用新的数据管理器处理交互
  await gameDataManager.processInteraction(currentState.meta.gameId, currentState.meta.sessionId, {
    userMessage: {
      id: `user_${Date.now()}`,
      sender: 'user' as const,
      timestamp: new Date().toISOString(),
      content: {
        ask: userInput.prompt
      }
    },
      aiResponse: {
        id: `system_${Date.now() + 1}`,
        sender: 'system' as const,
        timestamp: new Date().toISOString(),
        content: {
          answer: agentAResult.answer || '好的，我已经依照主人的安排创作了剧情~',
          options: agentAResult.suggestedOptions || []
        }
      },
      novelSection: agentAResult.narrative ? {
        id: agentAResult.sectionsId || '', // 使用Agent A返回的sectionsId，如果为空则表示新增小节
        title: agentAResult.sectionTitle || '当前场景',
        text: agentAResult.narrative,
        timestamp: new Date().toISOString(),
        wordCount: agentAResult.narrative.length
      } : undefined,
      // 添加Agent A的环境信息（从Agent A的chaptersInfo获取）
      chapterInfo: agentAResult.chaptersInfo ? {
        id: agentAResult.chaptersInfo.id,
        chapterTitle: agentAResult.chaptersInfo.chapterTitle,
        time: agentAResult.chaptersInfo.time,
        location: agentAResult.chaptersInfo.location,
        environment: agentAResult.chaptersInfo.environment
      } : undefined,
      // 添加Agent C的角色更新 - 传递完整的角色数据
      characterUpdates: agentCResult?.updatedCharacters ?
        agentCResult.updatedCharacters.reduce((acc: any, char: any) => {
          // 对于新角色，传递完整数据；对于现有角色，只传递更新字段
          acc[char.id] = char; // 传递完整的角色对象
          return acc;
        }, {}) : undefined
    });

  // 构建包含环境信息的最终游戏状态
  let finalGameState = agentCResult?.updatedGameState || context.currentState;

    // 确保包含Agent A的环境信息，但保持session结构完整
    if (agentAResult.chaptersInfo) {
      console.log('[AIService] 合并Agent A的环境信息到最终状态');

    // 确保保留原有的session结构
    finalGameState = {
      ...context.currentState, // 保留原有的完整结构
      ...finalGameState, // 应用Agent C的更新
      novel: {
        ...context.currentState.novel,
        ...finalGameState.novel,
        plotSummary: agentAResult.narrative || finalGameState.novel?.plotSummary || context.currentState.novel?.plotSummary || '',
        currentChapter: agentAResult.chaptersInfo.id,
        currentSection: agentAResult.sectionTitle || '当前场景'
      }
    };

      console.log('[AIService] 环境信息合并完成，保持session结构完整');
    }

    // 处理Agent C的stateChangeLog生成newMessage（去重优化）
    let newMessage: any[] = [];
    console.log('[AIService] 检查agentCResult:', JSON.stringify(agentCResult, null, 2));
    if (agentCResult?.stateChangeLog && agentCResult.stateChangeLog.length > 0) {
      console.log('[AIService] 处理Agent C的stateChangeLog生成newMessage');

      // 用于去重的消息内容集合
      const seenMessages = new Set<string>();

      agentCResult.stateChangeLog.forEach((change: any) => {
        // 检查角色是否在场，只为在场角色生成状态变化通知
        const character = finalGameState.characters.find(c => c.id === change.id);
        if (!character || !character.isPresent) {
          console.log(`[AIService] 跳过不在场角色的状态变化通知: ${change.id} - "${change.msg}"`);
          return;
        }

        // 清理消息内容，移除重复信息
        let cleanMsg = change.msg;

        // 移除常见的重复短语
        cleanMsg = cleanMsg.replace(/在当前场景中[。，]?/g, '');
        cleanMsg = cleanMsg.replace(/在场景中[。，]?/g, '');
        cleanMsg = cleanMsg.trim();

        // 如果消息为空或已存在，跳过
        if (!cleanMsg || seenMessages.has(cleanMsg)) {
          console.log(`[AIService] 跳过重复或空消息: "${change.msg}"`);
          return;
        }

        seenMessages.add(cleanMsg);

        const changeMessage = {
          [change.id]: {
            time: new Date().toISOString(),
            msg: cleanMsg
          }
        };

        // 查找是否已存在该角色的变化记录
        const existingIdx = newMessage.findIndex(
          (item: any) => Object.keys(item)[0] === change.id
        );

        if (existingIdx !== -1) {
          // 更新现有记录
          newMessage[existingIdx] = changeMessage;
        } else {
          // 添加新记录，控制数组长度不超过3个（减少显示数量）
          if (newMessage.length >= 3) {
            newMessage.shift();
          }
          newMessage.push(changeMessage);
        }
      });

      console.log('[AIService] newMessage生成完成:', JSON.stringify(newMessage, null, 2));
    }

    // 合并newMessage到最终状态
    finalGameState = {
      ...finalGameState,
      newMessage: newMessage
    };

  // 应用Agent C的角色更新，并验证数据完整性
  if (agentCResult?.updatedCharacters && Array.isArray(agentCResult.updatedCharacters)) {
    console.log('[AIService] 应用Agent C的角色更新到最终状态');

    // 从当前状态开始，合并Agent C的更新
    let updatedCharacters = [...(currentState.characters || [])];

    agentCResult.updatedCharacters.forEach((update: any) => {
      const existingCharIndex = updatedCharacters.findIndex((char: any) => char.id === update.id);

      if (existingCharIndex !== -1) {
        // 更新现有角色
        const existingChar = updatedCharacters[existingCharIndex];
        updatedCharacters[existingCharIndex] = {
          ...existingChar,
          isPresent: update.isPresent !== undefined ? update.isPresent : existingChar.isPresent,
          attributeCollections: update.attributeCollections || existingChar.attributeCollections
        };
        console.log(`[AIService] 更新现有角色: ${existingChar.name} (${update.id})`);
      } else {
        // 添加新角色 - 验证和补全数据
        if (update.name && update.characterType && update.description) {
          const validatedCharacter = validateAndCompleteCharacter(update);
          updatedCharacters.push(validatedCharacter);
          console.log(`[AIService] 添加新角色: ${update.name} (${update.id})`);

          if (validatedCharacter._wasIncomplete) {
            console.warn(`[AIService] 新角色 ${update.id} 数据不完整，已自动补全`);
          }
        } else {
          console.warn(`[AIService] 跳过不完整的新角色: ${update.id}`, update);
        }
      }
    });

    finalGameState.characters = updatedCharacters;
    console.log(`[AIService] 角色更新完成，共${updatedCharacters.length}个角色`);
  } else {
    // 如果没有Agent C更新，保留原有角色数据
    console.log('[AIService] 没有Agent C角色更新，保留原有角色数据');
    finalGameState.characters = currentState.characters || [];
  }

  // 🎭 角色管理完全由Agent C负责
  // 程序不再进行自动角色检测，避免误判群众演员为重要角色
  console.log('[AIService] 🎭 角色管理完全由Agent C负责，程序不进行自动检测');

  console.log(`[AIService] 创作模式流程执行完成，总耗时: ${chainResult.totalExecutionTime}ms`);

    // 构建格式化的UI消息
    const creationFormattedMessage: FormattedDialogueMessage = {
      id: `system_${Date.now()}`,
      sender: 'system',
      content: {
        narrative: agentAResult.narrative || '故事继续...'
      },
      timestamp: new Date().toISOString(),
      options: agentAResult.suggestedOptions || []
    };

    const totalTime = Date.now() - startTime;
    console.log(`[AIService] 导演模式流程执行完成，总耗时: ${totalTime}ms`);

    return {
      updatedGameState: finalGameState,
      formattedUIContent: [creationFormattedMessage],
      suggestedOptions: agentAResult.suggestedOptions || [],
      sectionTitle: agentAResult.sectionTitle || '当前场景'
    };
  } catch (error) {
    console.error('[AIService] 导演模式流程执行失败:', error);
    throw error;
  }
}


// 5. 参与者模式链式流程
export async function executeParticipantFlow(currentState: GameSaveState, userInput: { prompt: string; characterId: string }): Promise<InteractionResponse> {
  try {
    // AgentB
    const { agentBOutput, targetCharacter } = await agentBProcess(currentState, userInput);
    // AgentC
    const agentCClient = await getOpenAIClient('agent_c');
    if (!agentCClient) throw new Error('无法初始化Agent C的OpenAI客户端');
    
    try {
      const systemBasePrompt = await loadPromptTemplate('system_base');
      // 尝试使用agent_c_prompt，如果失败则尝试agent_analyst
      let agentCPrompt;
      try {
        agentCPrompt = await loadPromptTemplate('agent_c_prompt');
        console.log('[AIService] 成功加载agent_c_prompt');
      } catch (error) {
        console.log('[AIService] 尝试加载agent_analyst作为备选');
        agentCPrompt = await loadPromptTemplate('agent_analyst');
      }
      
      const agentCContext = `\n${systemBasePrompt}\n\n${agentCPrompt}\n\n## 当前游戏状态\n${JSON.stringify(currentState, null, 2)}\n\n## 玩家对话\n玩家对 ${targetCharacter.name} 说: "${userInput.prompt}"\n\n## Agent B的输出 (角色 ${targetCharacter.name} 的实际回复内容)\n${agentBOutput}\n\n## 你的任务\n请分析角色对话并更新游戏状态。特别注意：在构建返回结果中的 \`formattedUIContent\` 数组时，如果消息的 \`sender\` 是 'ai' (代表角色的回复), 其 \`answer\` 字段必须只包含角色的直接语音回复 (即上述 "Agent B的输出" 内容)。不要在这些聊天消息的 \`answer\` 或 \`content\` 字段中包含任何叙事描述、场景细节或其他故事文本。对于 \`sender\` 是 'system' 的消息, \`answer\` 字段应为简短的状态或摘要，而不是小说内容。`;
      
      console.log('[AIService] 调用Agent C处理参与者模式对话');

      // 获取Agent C的配置参数
      const agentConfig = await getAgentConfig('agent_c');

      const agentCResponse = await callCompatibleChatCompletion(agentCClient, {
        model: await getModelName('agent_c'),
        messages: [
          { role: "system", content: agentCContext },
          { role: "user", content: `请根据我和 ${targetCharacter.name} 的对话 (他的回复是: "${agentBOutput}") 更新游戏状态，并生成UI显示内容。` }
        ],
        temperature: agentConfig.temperature,
        max_tokens: agentConfig.maxTokens,
        response_format: { type: "json_object" }
      });
      
      const agentCOutput = agentCResponse.choices[0].message.content;
      if (!agentCOutput) throw new Error('Agent C返回的内容为空');
      
      let interactionResponse: InteractionResponse;
      let cleanedOutput = agentCOutput;
      
      try {
        // 如果输出包含JSON代码块，尝试提取
        if (cleanedOutput.includes('```json')) {
          cleanedOutput = cleanedOutput.replace(/```json\n/g, '').replace(/```/g, '');
        } else if (cleanedOutput.includes('```')) {
          cleanedOutput = cleanedOutput.replace(/```.*\n/g, '').replace(/```/g, '');
        }
        
        interactionResponse = JSON.parse(cleanedOutput) as InteractionResponse;
        console.log('[AIService] 成功解析Agent C的JSON输出');
      } catch (jsonError) {
        console.error('[AIService] 解析Agent C的JSON输出失败:', jsonError);
        console.log('[AIService] Agent C原始输出:', agentCOutput.substring(0, 200) + '...');
        
        // 创建一个默认的交互响应
        const participantFormattedMessage: FormattedDialogueMessage = {
          id: `character_${Date.now()}`,
          sender: 'ai',
          content: {
            answer: agentBOutput
          },
          timestamp: new Date().toISOString(),
          characterId: targetCharacter.id
        };
        
        interactionResponse = {
          updatedGameState: currentState,
          formattedUIContent: [participantFormattedMessage],
          suggestedOptions: ["继续对话", "询问其他问题", "结束对话"]
        };
        
        console.log('[AIService] 创建了默认的交互响应');
      }
      
      // 确保 novel 结构正确
      if (interactionResponse.updatedGameState) {
        if (!interactionResponse.updatedGameState.novel) {
          interactionResponse.updatedGameState.novel = { ...currentState.novel };
        }
        
        // 读取现有的 novel 内容
        try {
          // 尝试读取现有的 novel_xxx.json 文件
          let existingNovel = await novelService.getNovelContent(
            currentState.meta.gameId,
            currentState.meta.sessionId
          );
          
          // 确保existingNovel是数组
          if (!existingNovel || !Array.isArray(existingNovel)) {
            console.log('[AIService] 角色模式：existingNovel不是数组，初始化为空数组');
            existingNovel = [];
          }
          
          // 如果读取成功，使用现有结构
          if (existingNovel.length > 0) {
            console.log('[AIService] 角色模式：找到现有 novel 结构，将在其基础上更新');
            
            // 获取当前章节ID
            const currentChapterId = interactionResponse.updatedGameState.novel.currentChapter || 'chapter-1';
            
            // 查找当前章节，如果不存在则创建
            let currentChapter = existingNovel.find(c => c.id === currentChapterId || c.chapter === currentChapterId);
            if (!currentChapter) {
              currentChapter = {
                id: currentChapterId,
                chapter: currentChapterId,
                chapter_title: generateChapterTitle(currentChapterId, interactionResponse.updatedGameState),
                sections: [],
                pre_summary: "故事继续...",
                post_summary: "故事发展中...",
                characters: [],
                backgroundImageUrl: "",
                visualPrompt: {
                  description: "",
                  style: "default"
                },
                time: "",
                location: "",
                environment: ""
              };
              existingNovel.push(currentChapter);
            }
            
            // 创建新小节
            const timestamp = new Date().toISOString();
            // 小节ID格式化为 chapter-1-X 或 dialogue-timestamp
            const dialogueId = currentChapterId.startsWith('chapter-') ? 
              `${currentChapterId}-${currentChapter.sections.length + 1}` : 
              `dialogue_${timestamp}`;
            const dialogueTitle = `与${targetCharacter.name}的对话`;
            const dialogueText = `${targetCharacter.name}说道："${agentBOutput}"`;

            // 检查是否已存在相同ID的小节
            const existingSectionIndex = currentChapter.sections.findIndex(
              s => s.id === dialogueId || (s.title === dialogueTitle && s.text === dialogueText)
            );

            if (existingSectionIndex >= 0) {
              // 如果找到相同内容的小节，不做任何更改
              console.log(`[AIService] 角色模式：发现重复对话内容，跳过添加`);
            } else {
              // 如果没有找到，添加新小节
              const newSection = {
                id: dialogueId,
                title: dialogueTitle,
                text: dialogueText,
                timestamp: timestamp
              };
              currentChapter.sections.push(newSection);
              console.log(`[AIService] 角色模式：添加新对话小节`);
            }
            
            // 更新 novel.chapters
            (interactionResponse.updatedGameState.novel as any).chapters = existingNovel;
            
            // 保存更新后的 novel 内容
            await novelService.saveNovelContent(
              currentState.meta.gameId,
              currentState.meta.sessionId,
              existingNovel
            );
          } else {
            // 如果读取失败，创建新结构
            console.log('[AIService] 角色模式：未找到现有 novel 结构，创建新结构');
            
            const novelChapters = [];
            const currentChapterId = interactionResponse.updatedGameState.novel.currentChapter || 'chapter-1';
            const timestamp = new Date().toISOString();
            
            // 创建章节和小节
            const chapter = {
              id: currentChapterId,
              chapter: currentChapterId,
              chapter_title: generateChapterTitle(currentChapterId, interactionResponse.updatedGameState),
              sections: [{
                id: currentChapterId.startsWith('chapter-') ? `${currentChapterId}-1` : `dialogue_${timestamp}`,
                title: `与${targetCharacter.name}的对话`,
                text: `${targetCharacter.name}说道："${agentBOutput}"`,
                timestamp: timestamp
              }],
              pre_summary: "故事继续...",
              post_summary: "故事发展中...",
              characters: [targetCharacter.id],
              backgroundImageUrl: "",
              visualPrompt: {
                description: "",
                style: "default"
              },
              time: "",
              location: "",
              environment: ""
            };
            
            novelChapters.push(chapter);
            (interactionResponse.updatedGameState.novel as any).chapters = novelChapters;
            
            // 保存新创建的 novel 内容
            await novelService.saveNovelContent(
              currentState.meta.gameId,
              currentState.meta.sessionId,
              novelChapters
            );
          }
        } catch (error) {
          console.error('[AIService] 角色模式：处理 novel 结构时出错:', error);
          
          // 出错时，创建基本结构
          const novelChapters = [];
          const currentChapterId = interactionResponse.updatedGameState.novel.currentChapter || 'chapter-1';
          const timestamp = new Date().toISOString();
          
          const chapter = {
            id: currentChapterId,
            chapter: currentChapterId,
            chapter_title: currentChapterId === 'chapter-1' ? "第一章" : currentChapterId,
            sections: [{
              id: currentChapterId.startsWith('chapter-') ? `${currentChapterId}-1` : `dialogue_${timestamp}`,
              title: `与${targetCharacter.name}的对话`,
              text: `${targetCharacter.name}说道："${agentBOutput}"`,
              timestamp: timestamp
            }],
            pre_summary: "故事继续...",
            post_summary: "故事发展中...",
            characters: [targetCharacter.id],
            backgroundImageUrl: "",
            visualPrompt: {
              description: "",
              style: "default"
            },
            time: "",
            location: "",
            environment: ""
          };
          
          novelChapters.push(chapter);
          (interactionResponse.updatedGameState.novel as any).chapters = novelChapters;
          
          // 保存基本结构
          await novelService.saveNovelContent(
            currentState.meta.gameId,
            currentState.meta.sessionId,
            novelChapters
          );
        }
      }
      
      // 安全更新游戏状态，确保不会返回undefined或无效数据
      if (interactionResponse.updatedGameState) {
        interactionResponse.updatedGameState = safeUpdateGameState(interactionResponse.updatedGameState, currentState);
      } else {
        interactionResponse.updatedGameState = currentState;
      }
      
      return interactionResponse;
    } catch (error) {
      console.error('[AIService] 参与者模式处理失败:', error);
      throw error;
    }
  } catch (error) {
    console.error('[AIService] 参与者模式流程执行失败:', error);
    
    // 创建一个默认的错误响应
    const errorMessage: FormattedDialogueMessage = {
      id: `error_${Date.now()}`,
      sender: 'system',
      content: {
        narrative: '处理您的请求时遇到了问题，请稍后再试。'
      },
      timestamp: new Date().toISOString(),
      options: ["尝试其他指令", "与角色交谈", "探索周围环境"]
    };
    
    // 返回原始游戏状态和错误消息
    return {
      updatedGameState: currentState,
      formattedUIContent: [errorMessage],
      suggestedOptions: errorMessage.options || []
    };
  }
}

/**
 * 根据用户输入生成完整的游戏世界设计
 * @param input 用户输入的游戏名称、简介和背景信息
 * @returns 生成的游戏世界设计数据
 */
export async function generateGameWorld(input: {
  worldName: string;
  shortDescription: string;
  background: string;
  openingNarrative: string;
}): Promise<CreatorStudioFormData> {
  console.log('[AIService] Generating game world with input:', input);
  
  try {
    // 初始化客户端
    const client = await getOpenAIClient('default');
    
    if (!client) {
      console.log('[AIService] Using mock data for game world generation (no valid client)');
      return generateMockGameWorld(input);
    }
    
    // 获取模型名称
    const modelName = await getModelName('default');
    
    // 加载创建世界的提示词模板
    let promptTemplate = await loadPromptTemplate('creates_world');
    
    // 替换模板中的占位符
    promptTemplate = promptTemplate
      .replace('{{游戏名称}}', input.worldName)
      .replace('{{游戏简介}}', input.shortDescription)
      .replace('{{游戏内容}}', input.background);
    
    console.log('[AIService] Calling OpenAI API to generate game world...');

    // 获取默认配置参数
    const config = await readLegacyConfig();
    const defaultConfig = config.openai?.default;
    const temperature = defaultConfig?.temperature || 0.7;
    const maxTokens = defaultConfig?.maxTokens || 4000;

    // 调用OpenAI API
    const response = await client.chat.completions.create({
      model: modelName,
      messages: [
        { role: 'system', content: promptTemplate },
        { role: 'user', content: `请根据以下信息创建一个完整的游戏世界：\n游戏名称：${input.worldName}\n游戏简介：${input.shortDescription}\n游戏背景：${input.background}\n开场白：${input.openingNarrative}` }
      ],
      temperature: temperature,
      max_tokens: maxTokens,
      response_format: { type: "json_object" }
    });
    
    // 提取JSON响应
    const content = response.choices[0]?.message?.content || '';
    console.log('[AIService] Received response from OpenAI:', content.substring(0, 200) + '...');
    
    try {
      // 解析JSON响应
      const jsonResponse = JSON.parse(content);
      
      // 转换为CreatorStudioFormData格式
      const gameWorld = convertJsonResponseToGameWorld(jsonResponse, input);
      
      console.log('[AIService] Successfully generated game world');
      return gameWorld;
    } catch (parseError) {
      console.error('[AIService] Failed to parse JSON response:', parseError);
      console.error('[AIService] Raw response:', content);
      return generateMockGameWorld(input);
    }
  } catch (error) {
    console.error('[AIService] Error generating game world:', error);
    return generateMockGameWorld(input);
  }
}

/**
 * 将AI生成的JSON响应转换为CreatorStudioFormData格式
 */
function convertJsonResponseToGameWorld(jsonResponse: any, input: {
  worldName: string;
  shortDescription: string;
  background: string;
  openingNarrative: string;
}): CreatorStudioFormData {
  // 确保scenes数组中的每个场景都有id
  const scenes = (jsonResponse.scenes || []).map((scene: any, index: number) => ({
    id: scene.id || `scene_${index + 1}`,
    name: scene.name || `场景 ${index + 1}`,
    narrativeDescription: scene.narrativeDescription || '',
    visualPrompt: scene.visualPrompt || '',
    isInitial: scene.isInitial || index === 0
  }));
  
  // 确保至少有一个场景
  if (scenes.length === 0) {
    scenes.push({
      id: 'scene_1',
      name: '初始场景',
      narrativeDescription: '这是游戏的初始场景。',
      visualPrompt: `${input.worldName}, initial scene, atmospheric lighting`,
      isInitial: true
    });
  }
  
  // 确保属性组和属性都有id
  const attributeGroups = (jsonResponse.attributeSystem?.groups || jsonResponse.characterAttributeSchema?.groups || []).map((group: any, groupIndex: number) => ({
    id: group.id || `group_${groupIndex + 1}`,
    name: group.name || `属性组 ${groupIndex + 1}`,
    fields: (group.fields || []).map((field: any, fieldIndex: number) => ({
      id: field.id || `attr_${groupIndex + 1}_${fieldIndex + 1}`,
      name: field.name || `属性 ${fieldIndex + 1}`,
      displayType: field.displayType || 'progress' as const,
      maxValue: field.maxValue || (field.displayType === 'starrating' ? 5 : 100),
      description: field.description || ''
    }))
  }));
  
  // 确保至少有一个属性组
  if (attributeGroups.length === 0) {
    // 不再预设属性系统，而是创建一个空的属性组，让AI在下次调用时生成
    attributeGroups.push({
      id: 'group_1',
      name: '基本属性',
      fields: [
        {
          id: 'attr_1_1',
          name: '属性1',
          displayType: 'progress' as const,
          maxValue: 100,
          description: '请通过AI生成更多符合游戏主题的属性'
        }
      ]
    });
  }
  
  // 确保角色都有id和必要的属性
  const characters = (jsonResponse.characters || []).map((character: any, index: number) => {
    // 构建角色的属性值映射
    const attributes: Record<string, any> = {};
    
    // 处理attributeCollections结构(AI生成的格式)
    if (character.attributeCollections && Array.isArray(character.attributeCollections)) {
      character.attributeCollections.forEach((collection: any) => {
        if (collection.fields && Array.isArray(collection.fields)) {
          collection.fields.forEach((field: any) => {
            if (field.id && field.value !== undefined) {
              // 根据字段类型处理值
              if (field.type === 'progress' && typeof field.value === 'object') {
                attributes[field.id] = field.value.current || 50;
              } else if (field.type === 'starrating') {
                attributes[field.id] = field.value || 3;
              } else {
                attributes[field.id] = field.value;
              }
            }
          });
        }
      });
    }
    
    // 如果没有通过attributeCollections获取到属性，则尝试从attributes获取
    if (Object.keys(attributes).length === 0 && character.attributes) {
      Object.assign(attributes, character.attributes);
    }
    
    // 如果仍然没有属性，为角色分配默认属性值
    if (Object.keys(attributes).length === 0) {
      attributeGroups.forEach((group: { id: string; name: string; fields: any[] }) => {
        group.fields.forEach((field: { id: string; name: string; displayType: string; maxValue?: number }) => {
          // 根据属性类型设置默认值
          if (field.displayType === 'progress') {
            attributes[field.id] = Math.floor(Math.random() * 70) + 30; // 30-100之间的随机值
          } else if (field.displayType === 'starrating') {
            attributes[field.id] = Math.floor(Math.random() * 3) + 2; // 2-5之间的随机值
          } else if (field.displayType === 'tags') {
            attributes[field.id] = ['默认标签'];
          } else {
            attributes[field.id] = '默认值';
          }
        });
      });
    }
    
    // 将旧的attributes格式转换为新的attributeCollections格式
    const attributeCollections = [];

    // 为每个属性组创建一个collection
    attributeGroups.forEach((group: any) => {
      const fields: any[] = [];

      group.fields.forEach((fieldDef: any) => {
        if (attributes[fieldDef.id] !== undefined) {
          fields.push({
            id: fieldDef.id,
            label: fieldDef.name,
            type: fieldDef.displayType,
            value: attributes[fieldDef.id],
            reason: '初始属性'
          });
        }
      });

      if (fields.length > 0) {
        attributeCollections.push({
          id: group.id,
          label: group.name,
          fields: fields
        });
      }
    });

    return {
      id: character.id || `char_${index + 1}`,
      name: character.name || `角色 ${index + 1}`,
      characterType: character.characterType || 'npc',
      description: character.description || '',
      portraitUrl: character.portraitUrl || '',
      aiModels: {
        imageBaseModel: character.aiModels?.imageBaseModel || 'stable-diffusion-xl',
        imageLoRAModel: character.aiModels?.imageLoRAModel || 'fantasy-style',
        ttsModel: character.aiModels?.ttsModel || 'azure-neural',
        voiceId: character.aiModels?.voiceId || 'zh-CN-XiaoxiaoNeural'
      },
      attributeCollections
    };
  });
  
  // 确保至少有一个角色
  if (characters.length === 0) {
    characters.push({
      id: 'char_1',
      name: '主角',
      characterType: 'player',
      description: '玩家控制的主要角色',
      aiModels: {
        imageBaseModel: 'stable-diffusion-xl',
        ttsModel: 'azure-neural',
        voiceId: 'zh-CN-YunxiNeural'
      },
      attributes: {}
    });
  }
  
  // 构建完整的游戏世界数据
  return {
    overview: {
      worldName: jsonResponse.overview?.worldName || input.worldName,
      shortDescription: jsonResponse.overview?.shortDescription || input.shortDescription,
      background: jsonResponse.overview?.background || input.background,
      coverImageUrl: jsonResponse.overview?.coverImageUrl || '',
      tags: jsonResponse.overview?.tags || [],
      openingNarrative: jsonResponse.overview?.openingNarrative || input.openingNarrative
    },
    scenes,
    characterAttributeSchema: {
      groups: attributeGroups
    },
    characters,
    worldSystemGuide: {
      attributeSystem: {
        description: '角色DNA中各种属性的详细说明和定义',
        attributes: [
          {
            dnaAttribute: '等级',
            description: '角色的整体实力和经验水平，决定角色在世界中的地位和能力',
            valueRange: '1级起始，理论上无上限',
            changeConditions: '通过完成任务、探索、战斗等活动获得经验值提升等级'
          },
          {
            dnaAttribute: '体力',
            description: '角色的身体素质和耐力，影响持续活动的能力',
            valueRange: '0-100',
            changeConditions: '通过体力活动提升，过度劳累会降低'
          },
          {
            dnaAttribute: '智力',
            description: '角色的学习能力和解决问题的能力，影响思维和判断',
            valueRange: '0-100',
            changeConditions: '通过学习、思考、解谜等活动提升'
          }
        ]
      },
      coreMechanics: {
        description: `# 核心机制

## 游戏循环
- 探索世界和环境
- 与角色互动对话
- 面对挑战和选择
- 获得成长和奖励
- 解锁新的内容

## 冲突解决
- 通过对话选择解决冲突
- 基于角色属性进行判定
- 策略决策影响结果

## 角色发展
- 通过行动获得经验
- 属性提升影响能力
- 解锁新的技能和选项

## 资源管理
- 时间和精力的分配
- 关系和声望的维护
- 物品和金钱的使用`
      },
      worldGuidelines: {
        description: `# 世界准则

## AI行为规范

### 应该遵守的规则
- 保持角色的一致性和真实性
- 根据角色属性和背景做出合理反应
- 推进故事发展，创造有趣的互动
- 尊重玩家的选择和决定
- 维护游戏世界的逻辑和设定

### 禁止的行为
- 不要违背角色的基本设定
- 不要做出不符合世界观的行为
- 不要忽略玩家的输入和选择
- 不要破坏游戏的平衡性
- 不要产生不当或有害的内容

## 世界特有规律
- 描述这个世界独有的物理法则
- 社会规范和文化特色
- 魔法或科技的运作方式
- 角色互动的基本原则

## 价值观和道德标准
- 这个世界认为什么是对的
- 什么行为会被谴责
- 如何处理道德冲突`
      }
    },
    cg: [{ image: '', info: '' }] // 添加CG属性
  };
}

/**
 * 生成模拟的游戏世界数据
 */
function generateMockGameWorld(input: {
  worldName: string;
  shortDescription: string;
  background: string;
  openingNarrative: string;
}): CreatorStudioFormData {
  console.log('[AIService] Generating mock game world with input:', input);
  
  // 创建一个随机ID生成函数
  const generateId = (prefix: string) => `${prefix}_${Math.random().toString(36).substring(2, 10)}`;
  
  // 基础变量
  let firstSceneName = '起始场景';
  let firstSceneDesc = `${input.worldName}的开始。${input.shortDescription}`;
  let firstSceneVisual = `${input.worldName}, atmospheric, establishing shot`;
  let secondSceneName = '探索区域';
  let secondSceneDesc = '一个充满可能性的区域，等待探索。';
  let secondSceneVisual = 'exploration area, mysterious, adventure';
  let tags = ['冒险', '探索', '互动'];
  
  // 根据输入的游戏名称和描述，生成一些随机的属性组和属性
  const attributeGroups = [];
  
  // 生成第一个属性组 - 核心属性
  const group1 = {
    id: generateId('group'),
    name: '核心属性',
    fields: [
      {
        id: generateId('attr'),
        name: '潜力',
        displayType: 'progress' as const,
        maxValue: 100,
        description: '角色的成长潜力和适应能力'
      },
      {
        id: generateId('attr'),
        name: '专注',
        displayType: 'progress' as const,
        maxValue: 100,
        description: '角色集中注意力的能力'
      },
      {
        id: generateId('attr'),
        name: '意志',
        displayType: 'progress' as const,
        maxValue: 100,
        description: '角色的精神力量和决心'
      }
    ]
  };
  attributeGroups.push(group1);
  
  // 生成第二个属性组 - 技能
  const group2 = {
    id: generateId('group'),
    name: '技能',
    fields: [
      {
        id: generateId('attr'),
        name: '洞察力',
        displayType: 'starrating' as const,
        maxValue: 5,
        description: '角色观察和理解事物的能力'
      },
      {
        id: generateId('attr'),
        name: '表达',
        displayType: 'starrating' as const,
        maxValue: 5,
        description: '角色表达想法和情感的能力'
      },
      {
        id: generateId('attr'),
        name: '创造力',
        displayType: 'starrating' as const,
        maxValue: 5,
        description: '角色创新和解决问题的能力'
      }
    ]
  };
  attributeGroups.push(group2);
  
  // 生成第三个属性组 - 特质
  const group3 = {
    id: generateId('group'),
    name: '特质',
    fields: [
      {
        id: generateId('attr'),
        name: '身份标签',
        displayType: 'tags' as const,
        description: '角色的社会身份和背景'
      },
      {
        id: generateId('attr'),
        name: '性格特点',
        displayType: 'tags' as const,
        description: '角色的主要性格特点'
      }
    ]
  };
  attributeGroups.push(group3);
  
  // 生成角色
  const characters = [];
  
  // 生成玩家角色
  const playerCharacter = {
    id: generateId('char'),
    name: '探索者',
    characterType: 'player' as const,
    description: `${input.worldName}的主要探索者，充满好奇心和冒险精神。`,
    portraitUrl: '',
    aiModels: {
      imageBaseModel: 'stable-diffusion-xl',
      imageLoRAModel: 'adventure-style',
      ttsModel: 'azure-neural',
      voiceId: 'zh-CN-YunxiNeural'
    },
    attributeCollections: [
      {
        id: group1.id,
        label: group1.name,
        fields: [
          {
            id: group1.fields[0].id,
            label: group1.fields[0].name,
            type: group1.fields[0].displayType,
            value: 85,
            reason: '初始潜力'
          },
          {
            id: group1.fields[1].id,
            label: group1.fields[1].name,
            type: group1.fields[1].displayType,
            value: 75,
            reason: '初始专注'
          },
          {
            id: group1.fields[2].id,
            label: group1.fields[2].name,
            type: group1.fields[2].displayType,
            value: 80,
            reason: '初始意志'
          }
        ]
      },
      {
        id: group2.id,
        label: group2.name,
        fields: [
          {
            id: group2.fields[0].id,
            label: group2.fields[0].name,
            type: group2.fields[0].displayType,
            value: 4,
            reason: '初始洞察力'
          },
          {
            id: group2.fields[1].id,
            label: group2.fields[1].name,
            type: group2.fields[1].displayType,
            value: 3,
            reason: '初始表达'
          },
          {
            id: group2.fields[2].id,
            label: group2.fields[2].name,
            type: group2.fields[2].displayType,
            value: 4,
            reason: '初始创造力'
          }
        ]
      },
      {
        id: group3.id,
        label: group3.name,
        fields: [
          {
            id: group3.fields[0].id,
            label: group3.fields[0].name,
            type: group3.fields[0].displayType,
            value: ['探索者', '冒险家'],
            reason: '初始身份标签'
          },
          {
            id: group3.fields[1].id,
            label: group3.fields[1].name,
            type: group3.fields[1].displayType,
            value: ['好奇', '勇敢', '坚定'],
            reason: '初始性格特点'
          }
        ]
      }
    ]
  };
  characters.push(playerCharacter);
  
  // 生成NPC角色
  const npcCharacter = {
    id: generateId('char'),
    name: '向导',
    characterType: 'npc' as const,
    description: `${input.worldName}的知识渊博的向导，为探索者提供帮助和建议。`,
    portraitUrl: '',
    aiModels: {
      imageBaseModel: 'stable-diffusion-xl',
      imageLoRAModel: 'wise-style',
      ttsModel: 'azure-neural',
      voiceId: 'zh-CN-YunyangNeural'
    },
    attributeCollections: [
      {
        id: group1.id,
        label: group1.name,
        fields: [
          {
            id: group1.fields[0].id,
            label: group1.fields[0].name,
            type: group1.fields[0].displayType,
            value: 70,
            reason: '向导的潜力'
          },
          {
            id: group1.fields[1].id,
            label: group1.fields[1].name,
            type: group1.fields[1].displayType,
            value: 90,
            reason: '向导的专注'
          },
          {
            id: group1.fields[2].id,
            label: group1.fields[2].name,
            type: group1.fields[2].displayType,
            value: 85,
            reason: '向导的意志'
          }
        ]
      },
      {
        id: group2.id,
        label: group2.name,
        fields: [
          {
            id: group2.fields[0].id,
            label: group2.fields[0].name,
            type: group2.fields[0].displayType,
            value: 5,
            reason: '向导的洞察力'
          },
          {
            id: group2.fields[1].id,
            label: group2.fields[1].name,
            type: group2.fields[1].displayType,
            value: 4,
            reason: '向导的表达'
          },
          {
            id: group2.fields[2].id,
            label: group2.fields[2].name,
            type: group2.fields[2].displayType,
            value: 3,
            reason: '向导的创造力'
          }
        ]
      },
      {
        id: group3.id,
        label: group3.name,
        fields: [
          {
            id: group3.fields[0].id,
            label: group3.fields[0].name,
            type: group3.fields[0].displayType,
            value: ['向导', '学者'],
            reason: '向导的身份标签'
          },
          {
            id: group3.fields[1].id,
            label: group3.fields[1].name,
            type: group3.fields[1].displayType,
            value: ['智慧', '耐心', '神秘'],
            reason: '向导的性格特点'
          }
        ]
      }
    ]
  };
  characters.push(npcCharacter);
  
  // 构建完整的游戏世界数据
  return {
    overview: {
      worldName: input.worldName,
      shortDescription: input.shortDescription,
      background: input.background,
      coverImageUrl: '',
      tags,
      openingNarrative: input.openingNarrative
    },
    scenes: [
      {
        id: generateId('scene'),
        name: firstSceneName,
        narrativeDescription: firstSceneDesc,
        visualPrompt: firstSceneVisual,
        isInitial: true
      },
      {
        id: generateId('scene'),
        name: secondSceneName,
        narrativeDescription: secondSceneDesc,
        visualPrompt: secondSceneVisual,
        isInitial: false
      }
    ],
    characterAttributeSchema: {
      groups: attributeGroups
    },
    characters,
    worldSystemGuide: {
      attributeSystem: {
        description: `${input.worldName}中的角色属性系统，包含各种影响角色能力和故事发展的数值和状态`,
        attributes: [
          {
            dnaAttribute: '等级',
            description: '角色的整体实力和经验水平，决定角色在世界中的地位和能力',
            valueRange: '1级起始，理论上无上限',
            changeConditions: '通过完成任务、探索、战斗等活动获得经验值提升等级'
          },
          {
            dnaAttribute: '体力',
            description: '角色的身体素质和耐力，影响持续活动的能力',
            valueRange: '0-100',
            changeConditions: '通过体力活动提升，过度劳累会降低'
          },
          {
            dnaAttribute: '智力',
            description: '角色的学习能力和解决问题的能力，影响思维和判断',
            valueRange: '0-100',
            changeConditions: '通过学习、思考、解谜等活动提升'
          },
          {
            dnaAttribute: '魅力',
            description: '角色的社交能力和个人魅力，影响人际关系',
            valueRange: '0-100',
            changeConditions: '通过社交互动、成功的交流提升'
          }
        ]
      },
      coreMechanics: {
        description: `# 核心机制

## 游戏循环
- 探索世界和环境
- 与角色互动对话
- 面对挑战和选择
- 获得成长和奖励
- 解锁新的内容

## 冲突解决
- 通过对话选择解决冲突
- 基于角色属性进行判定
- 策略决策影响结果

## 角色发展
- 通过行动获得经验
- 属性提升影响能力
- 解锁新的技能和选项

## 资源管理
- 时间和精力的分配
- 关系和声望的维护
- 物品和金钱的使用`
      },
      worldGuidelines: {
        description: `# 世界准则

## AI行为规范

### 应该遵守的规则
- 保持角色的一致性和真实性
- 根据角色属性和背景做出合理反应
- 推进故事发展，创造有趣的互动
- 尊重玩家的选择和决定
- 维护${input.worldName}世界的逻辑和设定

### 禁止的行为
- 不要违背角色的基本设定
- 不要做出不符合世界观的行为
- 不要忽略玩家的输入和选择
- 不要破坏游戏的平衡性
- 不要产生不当或有害的内容

## 世界特有规律
- 描述${input.worldName}世界独有的物理法则
- 社会规范和文化特色
- 魔法或科技的运作方式
- 角色互动的基本原则

## 价值观和道德标准
- 这个世界认为什么是对的
- 什么行为会被谴责
- 如何处理道德冲突`
      }
    },
    cg: [{ image: '', info: '' }] // 添加CG属性
  };
}

/**
 * 生成游戏初始场景
 */
export async function generateInitialScene(input: {
  worldName: string;
  shortDescription: string;
  background: string;
  openingNarrative?: string;
  characters?: Array<{id: string; name: string; description: string}>;
}): Promise<{
  scene: {
    id: string;
    name: string;
    narrativeDescription: string;
    visualPrompt: string;
    isInitial: boolean;
    chapter: string;
    time: string;
    location: string;
    environment: string;
  };
  suggestedOptions: string[];
  charactersPresent?: string[]; // 添加在场角色ID列表
  sectionTitle?: string; // 添加小节标题
}> {
  console.log('[AIService] 开始生成初始场景:', input.worldName);
  
  try {
    // 初始化客户端
    const client = await getOpenAIClient('default');
    
    if (!client) {
      console.error('[AIService] OpenAI客户端初始化失败，使用备用数据');
      return generateRichInitialScene(input);
    }
    
    // 获取模型名称
    const model = await getModelName('default');
    console.log(`[AIService] 使用模型 ${model} 生成初始场景`);
    
    // 构建提示词
    const characterDescriptions = input.characters ? 
      input.characters.map(c => `- ${c.name}: ${c.description}`).join('\n') : 
      '无角色信息';
    
    const prompt = `
# 初始场景生成请求
## 世界设定
- 世界名称: ${input.worldName}
- 简介: ${input.shortDescription}
- 背景故事: ${input.background}

## 角色列表
${characterDescriptions}

## 任务
请为这个世界生成一个引人入胜的初始场景，包括:
1. 场景名称
2. 详细的叙事描述 (300-500字)
3. 视觉提示词 (用于AI图像生成)
4. 时间、地点和环境描述
5. 3-5个玩家可以选择的行动选项
6. 哪些角色在这个场景中出现 (提供角色ID列表)
7. 场景小节的标题

## 输出格式
请使用JSON格式，包含以下字段:
{
  "scene": {
    "id": "scene_initial",
    "name": "场景名称",
    "narrativeDescription": "详细的叙事描述",
    "visualPrompt": "用于AI图像生成的视觉提示词",
    "isInitial": true,
    "chapter": "第一章",
    "time": "时间描述",
    "location": "地点描述",
    "environment": "环境描述"
  },
  "suggestedOptions": ["选项1", "选项2", "选项3"],
  "charactersPresent": ["角色ID1", "角色ID2"],
  "sectionTitle": "小节标题"
}
`;
    
    console.log(`[AIService] 发送初始场景生成请求，提示词长度: ${prompt.length}`);

    // 获取默认配置参数
    const config = await readLegacyConfig();
    const defaultConfig = config.openai?.default;
    const temperature = defaultConfig?.temperature || 0.7;
    const maxTokens = defaultConfig?.maxTokens || 2000;

    try {
      // 调用API
      const response = await callCompatibleChatCompletion(client, {
        model: model,
        messages: [
          { role: 'system', content: '你是一个创意游戏设计AI，擅长生成引人入胜的场景描述和故事情节。' },
          { role: 'user', content: prompt }
        ],
        temperature: temperature,
        max_tokens: maxTokens,
        response_format: { type: "json_object" }
      });
      
      if (!response || !response.choices || !response.choices[0]?.message?.content) {
        console.error('[AIService] API返回空响应或格式不正确，使用备用数据');
        return generateRichInitialScene(input);
      }
      
      const content = response.choices[0].message.content;
      console.log(`[AIService] API返回响应，长度: ${content.length}`);
      
      try {
        // 解析JSON
        const jsonResponse = JSON.parse(content);
        
        // 验证必要字段
        if (!jsonResponse.scene || !jsonResponse.scene.narrativeDescription) {
          console.error('[AIService] API返回的JSON缺少必要字段，使用备用数据');
          return generateRichInitialScene(input);
        }
        
        // 确保所有必要字段都存在
        const result = {
          scene: {
            id: jsonResponse.scene.id || 'scene_initial',
            name: jsonResponse.scene.name || `${input.worldName}的初始场景`,
            narrativeDescription: jsonResponse.scene.narrativeDescription || `欢迎来到${input.worldName}的世界。${input.shortDescription}`,
            visualPrompt: jsonResponse.scene.visualPrompt || `${input.worldName}的初始场景，${input.shortDescription}`,
            isInitial: true,
            chapter: jsonResponse.scene.chapter || '第一章',
            time: jsonResponse.scene.time || '黎明时分',
            location: jsonResponse.scene.location || '起始位置',
            environment: jsonResponse.scene.environment || '平静而神秘的环境'
          },
          suggestedOptions: jsonResponse.suggestedOptions || ['探索周围环境', '与角色交谈', '查看物品'],
          charactersPresent: jsonResponse.charactersPresent || input.characters?.map(c => c.id) || [],
          sectionTitle: jsonResponse.sectionTitle || '初始场景'
        };
        
        console.log('[AIService] 成功生成初始场景');
        return result;
      } catch (parseError) {
        console.error('[AIService] 解析API响应失败:', parseError);
        console.log('[AIService] API原始响应:', content.substring(0, 200) + '...');
        return generateRichInitialScene(input);
      }
    } catch (apiError) {
      console.error('[AIService] API调用失败:', apiError);
      return generateRichInitialScene(input);
    }
  } catch (error) {
    console.error('[AIService] 生成初始场景时发生错误:', error);
    return generateRichInitialScene(input);
  }
}

/**
 * 生成丰富的初始场景（API调用失败时的备用方案）
 * 比generateMockInitialScene更加丰富，基于游戏信息生成较为详细的场景描述
 */
function generateRichInitialScene(input: {
  worldName: string;
  shortDescription: string;
  background: string;
  openingNarrative?: string;
  characters?: Array<{id: string; name: string; description: string}>;
}): {
  scene: {
    id: string;
    name: string;
    narrativeDescription: string;
    visualPrompt: string;
    isInitial: boolean;
    chapter: string;
    time: string;
    location: string;
    environment: string;
  };
  suggestedOptions: string[];
  charactersPresent?: string[];
  sectionTitle?: string;
} {
  // 从背景描述中提取关键词，确定场景类型
  let sceneType = '神秘';
  let chapterName = '序章';
  let locationName = input.worldName.includes('：') ? input.worldName.split('：')[1] + '入口' : '起始地点';
  let environmentDesc = '宁静而神秘';
  let timeDesc = '黎明时分';
  
  // 根据游戏背景关键词确定场景风格
  const background = input.background.toLowerCase();
  if (background.includes('修仙') || background.includes('仙侠') || background.includes('武侠')) {
    sceneType = '仙侠';
    chapterName = '修仙开篇';
    locationName = '山门前';
    environmentDesc = '灵气氤氲';
    timeDesc = '紫气东来之时';
  } else if (background.includes('魔法') || background.includes('奇幻') || background.includes('巫师')) {
    sceneType = '奇幻';
    chapterName = '法师之路';
    locationName = '魔法学院';
    environmentDesc = '魔法元素飘散';
    timeDesc = '星辰闪烁之夜';
  } else if (background.includes('未来') || background.includes('科幻') || background.includes('星际')) {
    sceneType = '科幻';
    chapterName = '星际序曲';
    locationName = '太空港';
    environmentDesc = '科技感十足';
    timeDesc = '宇宙黎明';
  }
  
  // 生成场景名称
  const sceneName = input.worldName.includes('：') ? 
    input.worldName.split('：')[1] + '初试' : 
    input.worldName + '的开始';
  
  // 生成叙事描述 - 优先使用开场白
  const narrativeTemplate = input.openingNarrative ?
    input.openingNarrative :
    [
      `${timeDesc}，你来到了${locationName}，${environmentDesc}的气息扑面而来。这里是${input.worldName}的世界，${input.shortDescription}`,
      `你环顾四周，思考着接下来的行动。${input.background.substring(0, 100)}...`,
      `一段全新的冒险即将开始，命运的齿轮已经开始转动。`
    ].join('\n\n');
  
  // 确定在场角色
  let charactersPresent: string[] = [];
  if (input.characters && input.characters.length > 0) {
    // 随机选择一半角色在场
    charactersPresent = input.characters
      .filter(() => Math.random() > 0.3) // 70%概率角色在场
      .map(c => c.id);
  }
  
  return {
    scene: {
      id: 'scene_initial',
      name: sceneName,
      narrativeDescription: narrativeTemplate,
      visualPrompt: `${input.worldName}的初始场景，${environmentDesc}，${timeDesc}，主角站在${locationName}`,
      isInitial: true,
      chapter: chapterName,
      time: timeDesc,
      location: locationName,
      environment: environmentDesc
    },
    suggestedOptions: [
      '探索周围环境',
      '与附近的角色交谈',
      '查看你的物品',
      '思考你的目标'
    ],
    charactersPresent,
    sectionTitle: sceneName
  };
}

/**
 * 生成模拟的初始场景
 */
function generateMockInitialScene(input: {
  worldName: string;
  shortDescription: string;
  background: string;
  characters?: Array<{id: string; name: string; description: string}>;
}): {
  scene: {
    id: string;
    name: string;
    narrativeDescription: string;
    visualPrompt: string;
    isInitial: boolean;
    chapter: string;
    time: string;
    location: string;
    environment: string;
  };
  suggestedOptions: string[];
  charactersPresent?: string[];
  sectionTitle?: string;
} {
  // 如果有角色，随机选择一半角色在场
  const charactersPresent = input.characters 
    ? input.characters
        .filter(() => Math.random() > 0.5) // 随机选择一半角色
        .map(c => c.id)
    : [];
    
  return {
    scene: {
      id: 'scene_initial',
      name: '初始场景',
      narrativeDescription: `欢迎来到《${input.worldName}》的世界。${input.shortDescription} 你的冒险即将开始。`,
      visualPrompt: `${input.worldName}的初始场景，展现了${input.shortDescription.split('。')[0]}`,
      isInitial: true,
      chapter: '序章',
      time: '黎明时分',
      location: '起始位置',
      environment: '平静而神秘的环境'
    },
    suggestedOptions: [
      '探索周围环境',
      '与附近的角色交谈',
      '查看你的物品'
    ],
    charactersPresent,
    sectionTitle: '初始场景'
  };
}

/**
 * AI重写小说小节
 */
export async function rewriteNovelSection(oldSection: any, prompt: string): Promise<any> {
  console.log('[AIService] 开始AI重写小节:', {
    sectionId: oldSection.id,
    sectionTitle: oldSection.title,
    prompt: prompt.substring(0, 100) + '...',
    originalTextLength: oldSection.text?.length || 0
  });

  try {
    // 获取OpenAI客户端
    const client = await getOpenAIClient('agent_a');
    if (!client) {
      console.warn('[AIService] OpenAI客户端未配置，使用mock重写');
      return {
        ...oldSection,
        id: oldSection.id,
        title: oldSection.title,
        text: `[AI重写内容] ${prompt}\n\n${oldSection.text}`
      };
    }

    // 构建重写提示词
    const systemPrompt = `你是一个专业的小说编辑和创作助手。你的任务是根据用户的要求重写小说章节内容。

重写要求：
1. 保持故事的核心情节和人物设定
2. 根据用户的具体要求调整写作风格、视角、情感色彩等
3. 确保重写后的内容流畅自然，符合小说的整体风格
4. 保持适当的篇幅，不要过度扩展或压缩
5. 输出纯文本内容，不要包含任何格式标记

请严格按照用户的重写要求进行创作。`;

    const userPrompt = `请根据以下要求重写这个小说章节：

重写要求：${prompt}

原始章节标题：${oldSection.title}
原始章节内容：
${oldSection.text}

请重写这个章节，确保：
1. 严格按照重写要求调整内容
2. 保持故事的连贯性和逻辑性
3. 输出完整的重写后章节内容
4. 不要添加任何解释或说明文字

重写后的章节内容：`;

    // 获取Agent A的配置参数
    const agentConfig = await getAgentConfig('agent_a');

    // 调用OpenAI API
    const response = await callCompatibleChatCompletion(client, {
      model: await getModelName('agent_a'),
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: agentConfig.temperature,
      max_tokens: agentConfig.maxTokens
    });

    let rewrittenText = response.choices[0]?.message?.content?.trim();

    if (!rewrittenText) {
      throw new Error('AI返回空内容');
    }

    // 对重写的内容进行变量替换
    // 注意：这里我们需要从某个地方获取玩家姓名，但是这个函数没有传入gameState
    // 我们可以从prompt中尝试提取，或者修改函数签名
    rewrittenText = rewrittenText.replace(/\{\{user\}\}/g, '玩家'); // 临时解决方案

    console.log('[AIService] AI重写完成:', {
      sectionId: oldSection.id,
      originalLength: oldSection.text?.length || 0,
      rewrittenLength: rewrittenText.length
    });

    return {
      ...oldSection,
      id: oldSection.id, // 保持ID不变
      title: oldSection.title, // 保持标题不变
      text: rewrittenText
    };

  } catch (error) {
    console.error('[AIService] AI重写失败:', error);

    // 如果AI调用失败，返回带有错误提示的内容
    return {
      ...oldSection,
      id: oldSection.id,
      title: oldSection.title,
      text: `[AI重写失败，请稍后重试]\n\n原始内容：\n${oldSection.text}`
    };
  }
}

/**
 * AI重写对话消息
 */
export async function rewriteChatMessage(oldMessage: any, prompt: string): Promise<any> {
  // 这里可调用实际AI服务，现用mock实现
  return {
    ...oldMessage,
    id: oldMessage.id, // 保持ID不变或生成新ID
    content: `[AI重写内容] ${prompt}\n${oldMessage.content}`
  };
}

/**
 * 生成初始游戏数据，包括场景、事件和角色属性
 * @param blueprint 游戏蓝图
 * @returns 初始游戏数据
 */
export async function generateInitialGameData(blueprint: any): Promise<{
  scene: any;
  events: any[];
  characters: any[];
  suggestedOptions: string[];
  sectionTitle: string;
}> {
  console.log('[AIService] 为游戏生成初始数据:', blueprint.overview.worldName);
  console.log('[AIService] 游戏简介:', blueprint.overview.shortDescription);
  console.log('[AIService] 角色数量:', blueprint.characters?.length || 0);
  
  try {
    // 检查OpenAI配置
    const client = await getOpenAIClient('default');
    if (!client) {
      console.error('[AIService] OpenAI客户端初始化失败，使用默认数据');
      return createDefaultGameData(blueprint);
    }
    
    console.log('[AIService] OpenAI客户端初始化成功，开始生成初始场景');
    
    // 1. 生成初始场景
    try {
      const initialSceneData = await generateInitialScene({
        worldName: blueprint.overview.worldName,
        shortDescription: blueprint.overview.shortDescription,
        background: blueprint.overview.background,
        openingNarrative: blueprint.overview.openingNarrative,
        characters: blueprint.characters
      });
      
      // 从AI响应中提取完整的叙事描述
      const narrativeDescription = initialSceneData.scene.narrativeDescription || 
        `欢迎来到《${blueprint.overview.worldName}》的世界。${blueprint.overview.shortDescription} 你的冒险即将开始。`;
      
      console.log(`[AIService] AI生成的初始场景叙事描述: ${narrativeDescription.substring(0, 100)}...`);
      
      // 2. 生成初始事件
      const initialEvent = {
        id: `event_${Date.now()}`,
        content: narrativeDescription,
        timestamp: new Date().toISOString(),
        isNarrative: true,
        sectionTitle: initialSceneData.sectionTitle || '初始场景',
        chapterId: initialSceneData.scene.chapter || '序章',
        chapterTitle: initialSceneData.scene.chapter || '序章'
      };
      
      // 3. 保留角色的原有属性数据
      const characters = blueprint.characters.map((char: any) => {
        // 设置角色在场状态
        const isPresent = initialSceneData.charactersPresent ?
          initialSceneData.charactersPresent.includes(char.id) :
          true;

        // 保留原有的attributeCollections，不要覆盖
        return {
          ...char,
          isPresent
        };
      });
      
      console.log('[AIService] 成功生成初始游戏数据');
      return {
        scene: initialSceneData.scene,
        events: [initialEvent],
        characters,
        suggestedOptions: initialSceneData.suggestedOptions,
        sectionTitle: initialSceneData.sectionTitle || '初始场景'
      };
    } catch (sceneError) {
      console.error('[AIService] 生成初始场景失败:', sceneError);
      return createDefaultGameData(blueprint);
    }
  } catch (error) {
    console.error('[AIService] 生成初始游戏数据失败:', error);
    return createDefaultGameData(blueprint);
  }
}

/**
 * 创建默认游戏数据，当AI调用失败时使用
 */
function createDefaultGameData(blueprint: any): {
  scene: any;
  events: any[];
  characters: any[];
  suggestedOptions: string[];
  sectionTitle: string;
} {
  console.log('[AIService] 创建默认游戏数据');
  
  // 为错误场景创建更加详细和丰富的默认叙事内容
  const defaultNarrative = generateRichDefaultNarrative(blueprint);
  
  // 返回默认数据
  return {
    scene: {
      id: 'scene_initial',
      name: '初始场景',
      narrativeDescription: defaultNarrative,
      visualPrompt: `${blueprint.overview.worldName}的初始场景`,
      isInitial: true,
      chapter: '序章',
      time: '黎明时分',
      location: '起始位置',
      environment: '平静而神秘的环境'
    },
    events: [{
      id: `event_${Date.now()}`,
      content: defaultNarrative,
      timestamp: new Date().toISOString(),
      isNarrative: true,
      sectionTitle: '初始场景',
      chapterId: '序章',
      chapterTitle: '序章'
    }],
    characters: blueprint.characters.map((char: any) => ({
      ...char,
      // 保留原有的attributeCollections，不要覆盖
      isPresent: true
    })),
    suggestedOptions: [
      '探索周围环境',
      '查看角色信息',
      '开始一段对话'
    ],
    sectionTitle: '初始场景'
  };
}

/**
 * 当API调用失败时，生成一个丰富的默认叙事内容
 * 基于游戏蓝图信息创建一个相对详细的初始叙事
 */
function generateRichDefaultNarrative(blueprint: any): string {
  const { worldName, shortDescription, background } = blueprint.overview;
  const mainCharacters = blueprint.characters.slice(0, 2).map((c: any) => c.name).join('和');
  
  // 根据游戏背景关键词选择合适的场景描述
  let sceneType = '神秘';
  let timeOfDay = '黎明时分';
  let weather = '晴朗';
  
  if (background.includes('修仙') || background.includes('道门') || background.includes('仙侠')) {
    sceneType = '仙境';
    timeOfDay = '紫气东来之时';
    weather = '云雾缭绕';
  } else if (background.includes('魔法') || background.includes('奇幻') || background.includes('巫师')) {
    sceneType = '魔法';
    timeOfDay = '星辰闪烁之夜';
    weather = '魔法粒子飘散';
  } else if (background.includes('赛博') || background.includes('未来') || background.includes('科幻')) {
    sceneType = '未来都市';
    timeOfDay = '霓虹闪烁之夜';
    weather = '电子雨';
  }
  
  // 构建多段落的叙事描述
  return `${timeOfDay}，${weather}的天气中，你来到了${worldName}的世界。${shortDescription}

你环顾四周，这个${sceneType}的地方充满了未知与可能。远处，${mainCharacters ? `${mainCharacters}似乎正在等待着什么。` : '一些角色的身影若隐若现。'}${background}

微风拂过，带来一丝若有若无的气息，仿佛在暗示着前方旅途的精彩与挑战。你深吸一口气，准备迎接这个世界带给你的冒险。

你的旅程，现在才刚刚开始。`;
}

/**
 * 从纯文本响应中提取选项和叙事内容
 * @param text AI返回的纯文本
 * @returns 提取的选项和叙事内容
 */
function extractOptionsFromText(text: string): { narrative: string; options: string[] } {
  // 默认值
  let narrative = text;
  let options: string[] = [];
  
  // 检查是否有选项列表（A. B. C.格式或1. 2. 3.格式或- - -格式）
  const optionPatterns = [
    /([A-Z]\.\s+.+?)(?=\s+[A-Z]\.\s+|\s*$)/g,  // A. 选项描述
    /(\d+\.\s+.+?)(?=\s+\d+\.\s+|\s*$)/g,      // 1. 选项描述
    /(\*\s+.+?)(?=\s+\*\s+|\s*$)/g,            // * 选项描述
    /(-\s+.+?)(?=\s+-\s+|\s*$)/g               // - 选项描述
  ];
  
  // 尝试匹配选项
  for (const pattern of optionPatterns) {
    const matches = text.match(pattern);
    if (matches && matches.length > 0) {
      // 提取选项
      options = matches.map(option => option.trim());
      
      // 查找选项部分的开始位置
      const firstOptionIndex = text.indexOf(matches[0]);
      if (firstOptionIndex > 0) {
        // 将选项之前的文本作为叙事内容
        narrative = text.substring(0, firstOptionIndex).trim();
      }
      
      break; // 找到选项后退出循环
    }
  }
  
  // 查找"你可以"、"你会选择"等提示选项的文本
  if (options.length === 0) {
    const choiceIndicators = [
      "你可以选择", "你会选择", "你可以", "你会", 
      "选择", "请选择", "可以选择", "可以"
    ];
    
    for (const indicator of choiceIndicators) {
      const index = text.lastIndexOf(indicator);
      if (index !== -1 && index > text.length / 2) {  // 只考虑文本后半部分
        const choiceText = text.substring(index);
        const choiceLines = choiceText.split('\n')
          .filter(line => line.trim().length > 0)
          .map(line => line.trim());
        
        if (choiceLines.length > 1) {
          // 第一行可能是"你可以选择："之类的提示，从第二行开始提取
          options = choiceLines.slice(1);
          narrative = text.substring(0, index).trim();
          break;
        }
      }
    }
  }
  
  // 如果还没找到选项，尝试查找以问号结尾的句子，可能是隐含的选项
  if (options.length === 0) {
    const questionPattern = /([^.!?]+\?)/g;
    const questions = text.match(questionPattern);
    
    if (questions && questions.length > 0) {
      // 只使用最后几个问题作为选项
      const lastQuestions = questions.slice(-3);  // 最多取3个问题
      options = lastQuestions.map(q => q.trim());
      
      // 移除叙事中的这些问题
      let narrativeTemp = text;
      lastQuestions.forEach(q => {
        narrativeTemp = narrativeTemp.replace(q, '');
      });
      narrative = narrativeTemp.trim();
    }
  }
  
  // 如果没有找到选项，则返回默认选项
  if (options.length === 0) {
    options = ["继续探索", "与角色交谈", "查看周围环境"];
  }
  
  return { narrative, options };
}

/**
 * 导出的Agent C分析方法，用于初始化时分析角色在场情况
 * @param currentState 当前游戏状态
 * @param agentAOutput Agent A 的输出（JSON字符串）
 * @param userInput 玩家输入或场景描述
 */
export async function executeAgentCAnalysis(currentState: GameSaveState, agentAOutput: string, userInput: string) {
  try {
    console.log('[AIService] 执行Agent C分析（使用新框架）...');

    // 读取 game_meta.json 获取 characterAttributeSchema
    let characterAttributeSchema = null;
    try {
      const gameMetaPath = path.join(db.getGameDataDir(), currentState.meta.gameId, 'game_meta.json');
      const gameMetaContent = await fs.readFile(gameMetaPath, 'utf-8');
      const gameMeta = JSON.parse(gameMetaContent);
      characterAttributeSchema = gameMeta.characterAttributeSchema;
      console.log('[AIService] 成功读取 characterAttributeSchema，属性组数量:', characterAttributeSchema?.groups?.length || 0);
    } catch (error) {
      console.warn('[AIService] 无法读取 characterAttributeSchema:', error.message);
    }

    // 使用新的Agent框架
    const { AgentC } = await import('./agentFramework.service.js');
    const agentC = new AgentC();

    const context = {
      gameId: currentState.meta.gameId,
      sessionId: currentState.meta.sessionId,
      userInput: userInput,
      currentState: currentState,
      characterAttributeSchema: characterAttributeSchema // 传递属性架构
    };

    const result = await agentC.process(context, agentAOutput);

    if (result.success && result.data) {
      console.log('[AIService] Agent C分析完成');
      // 转换新格式到旧格式，保持兼容性
      return {
        updatedCharacters: result.data.updatedCharacters || [],  // 修正字段名
        stateChangeLog: result.data.stateChangeLog || [],
        updatedGameState: result.data.updatedGameState || {}
      };
    } else {
      throw new Error(result.error || 'Agent C处理失败');
    }
  } catch (error) {
    console.error('[AIService] Agent C分析失败:', error);
    throw error;
  }
}

/**
 * 处理聊天模式 - 简化的处理流程，只更新聊天摘要
 */
async function handleChatMode(
  currentState: GameSaveState,
  userInput: { prompt: string; sectionId?: string; worldSystemGuide?: any },
  agentAResult: any,
  startTime: number
): Promise<InteractionResponse> {
  console.log('[AIService] 开始处理聊天模式');
  console.log('[DEBUG] 聊天模式 - Agent A 结果:', JSON.stringify(agentAResult, null, 2));
  console.log('[DEBUG] 聊天模式 - Agent A suggestedOptions:', agentAResult.suggestedOptions);

  try {
    // 1. 保存聊天消息
    const userMessageId = `user_${Date.now()}`;
    const aiMessageId = `system_${Date.now() + 1}`;

    const userMessage = {
      id: userMessageId,
      sender: 'user' as const,
      timestamp: new Date().toISOString(),
      content: {
        ask: userInput.prompt
      }
    };

    const aiMessage = {
      id: aiMessageId,
      sender: 'system' as const,
      timestamp: new Date().toISOString(),
      content: {
        answer: agentAResult.answer || '好的，我明白了~',
        options: agentAResult.suggestedOptions || []
      }
    };

    // 2. 保存聊天消息到文件
    await saveChatMessage(currentState.meta.gameId, currentState.meta.sessionId, userMessage);
    await saveChatMessage(currentState.meta.gameId, currentState.meta.sessionId, aiMessage);

    // 3. 只更新session的聊天摘要和UI状态，不更新复杂的游戏状态
    await updateSessionChatSummary(currentState, {
      lastAnswer: agentAResult.answer || '好的，我明白了~',
      suggestedOptions: agentAResult.suggestedOptions || [],
      lastMessageId: aiMessageId
    });

    const totalTime = Date.now() - startTime;
    console.log(`[AIService] 聊天模式处理完成，总耗时: ${totalTime}ms`);

    // 4. 构建格式化的UI消息
    const chatFormattedMessage: FormattedDialogueMessage = {
      id: aiMessageId,
      sender: 'system',
      content: {
        answer: agentAResult.answer || '好的，我明白了~'
      },
      timestamp: new Date().toISOString(),
      options: agentAResult.suggestedOptions || []
    };

    // 5. 返回简化的响应，不更新复杂的游戏状态
    return {
      updatedGameState: currentState, // 保持原有状态不变
      formattedUIContent: [chatFormattedMessage],
      suggestedOptions: agentAResult.suggestedOptions || [],
      sectionTitle: '聊天'
    };

  } catch (error) {
    console.error('[AIService] 聊天模式处理失败:', error);
    throw error;
  }
}

/**
 * 保存单条聊天消息
 */
async function saveChatMessage(gameId: string, sessionId: string, message: any) {
  try {
    // 使用 fileSystemService 的 appendChatMessage 添加新消息
    await fileSystemService.appendChatMessage(gameId, sessionId, message);

    console.log('[AIService] 聊天消息保存成功:', message.id);
  } catch (error) {
    console.error('[AIService] 保存聊天消息失败:', error);
    throw error;
  }
}

/**
 * 更新session的聊天摘要（不更新复杂状态）
 */
async function updateSessionChatSummary(
  currentState: GameSaveState,
  chatInfo: {
    lastAnswer: string;
    suggestedOptions: string[];
    lastMessageId: string;
  }
) {
  try {
    // 使用 fileSystemService 的 writeSession

    // 只更新聊天相关的字段，保持其他状态不变
    const updatedSession = {
      ...currentState,
      chatLog: {
        ...currentState.chatLog,
        system: {
          chatSummary: chatInfo.lastAnswer,
          lastMessageId: chatInfo.lastMessageId
        }
      },
      meta: {
        ...currentState.meta,
        lastUpdatedAt: new Date().toISOString()
      }
    };

    // 保存更新后的session数据
    await fileSystemService.writeSession(currentState.meta.gameId, currentState.meta.sessionId, updatedSession);

    console.log('[AIService] 聊天摘要更新成功');
  } catch (error) {
    console.error('[AIService] 更新聊天摘要失败:', error);
    throw error;
  }
}



