var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import * as fileSystemService from '../services/fileSystem.service.js';
import * as aiService from '../services/ai.service.js';
import * as novelService from '../services/novel.service.js';
import * as db from '../services/db.service.js';
import { gameInitializationService } from '../services/gameInitialization.service.js';
/**
 * 根据字段类型和规则获取默认值
 */
function getDefaultValueForField(field) {
    const displayType = field.displayType;
    const rules = field.rules;
    switch (displayType) {
        case 'number':
            if (rules && rules.range && rules.range.default !== undefined) {
                return rules.range.default;
            }
            return 0;
        case 'progress':
            if (rules && rules.range && rules.range.default !== undefined) {
                return rules.range.default;
            }
            return 50; // 默认50%
        case 'starrating':
            if (rules && rules.range && rules.range.default !== undefined) {
                return rules.range.default;
            }
            return 3; // 默认3星
        case 'tags':
            return [];
        case 'text':
        default:
            return '待设定';
    }
}
/**
 * 获取游戏所有会话列表
 */
export function handleGetSessions(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        const { gameId } = req.params;
        try {
            console.log(`[API] GET /api/game/${gameId}/sessions called`);
            const sessions = yield fileSystemService.listSessions(gameId);
            res.status(200).json(sessions);
        }
        catch (error) {
            console.error('[ERROR] in handleGetSessions:', error);
            res.status(500).json({ message: 'Failed to retrieve game sessions' });
        }
    });
}
/**
 * 获取特定会话的游戏状态
 */
export function handleGetSession(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        const { gameId, sessionId } = req.params;
        try {
            console.log(`[API] GET /api/game/${gameId}/session/${sessionId} called`);
            const gameState = yield fileSystemService.readSession(gameId, sessionId);
            if (!gameState) {
                res.status(404).json({ message: 'Game session not found' });
                return;
            }
            // gameName现在在meta字段中，interactionLog已废弃
            // 确保所有角色都有 isPresent 属性，但保持原有的在场状态
            if (gameState.characters && Array.isArray(gameState.characters)) {
                gameState.characters = gameState.characters.map(char => (Object.assign(Object.assign({}, char), { isPresent: char.isPresent !== undefined ? char.isPresent : false })));
                console.log('[GameSessionsController] 角色在场状态:', gameState.characters.map(c => `${c.name}: ${c.isPresent}`));
            }
            // 选项现在从聊天记录中获取，不再使用interactionLog
            let suggestedOptions = [];
            // 打印gameState调试
            console.log('handleGetSession 返回的 gameState:', JSON.stringify(gameState, null, 2));
            res.status(200).json({ gameState, suggestedOptions });
        }
        catch (error) {
            console.error('[ERROR] in handleGetSession:', error);
            res.status(500).json({ message: 'Failed to get game session' });
        }
    });
}
// 1. 构建基础 GameState - 严格按照标准格式，使用蓝图数据
function createBaseGameState(gameId, sessionId, playerName, gameName, saveName) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        // 读取游戏蓝图以获取角色和场景信息
        let blueprint = null;
        try {
            blueprint = yield fileSystemService.readBlueprint(gameId);
            console.log(`[GameSessionsController] 成功读取游戏蓝图: ${(_a = blueprint.overview) === null || _a === void 0 ? void 0 : _a.worldName}`);
            console.log(`[GameSessionsController] 蓝图中包含 ${((_b = blueprint.characters) === null || _b === void 0 ? void 0 : _b.length) || 0} 个角色`);
        }
        catch (error) {
            console.warn(`[GameSessionsController] 读取游戏蓝图失败，使用默认设置:`, error);
        }
        // 变量替换函数
        const replaceVariables = (text) => {
            if (!text)
                return text;
            return text.replace(/\{\{user\}\}/g, playerName);
        };
        // 从蓝图中获取角色数据，确保包含完整的属性结构
        const characters = (blueprint === null || blueprint === void 0 ? void 0 : blueprint.characters) ? blueprint.characters.map((char) => {
            console.log(`[GameSessionsController] 添加蓝图角色: ${char.name} (${char.id})`);
            // 转换属性集合格式，确保类型匹配
            const attributeCollections = (char.attributeCollections || []).map((collection, collectionIndex) => {
                console.log(`[GameSessionsController] createBaseGameState 转换属性集合 ${collectionIndex}:`, collection);
                return {
                    id: collection.id || `collection_${collectionIndex}`,
                    label: collection.label || collection.name || `属性组${collectionIndex + 1}`,
                    fields: (collection.attributes || collection.fields || []).map((attr, fieldIndex) => ({
                        id: attr.id || `field_${fieldIndex}`,
                        label: attr.label || attr.name || `属性${fieldIndex + 1}`,
                        type: attr.type || 'text',
                        value: attr.value || '',
                        reason: attr.reason || attr.description || '初始设置'
                    }))
                };
            });
            return {
                id: char.id,
                name: replaceVariables(char.name), // 替换角色名称中的变量
                avatarUrl: char.portraitUrl || null,
                description: replaceVariables(char.description), // 替换描述中的变量
                portraitUrl: char.portraitUrl || null,
                attributeCollections: attributeCollections,
                cg: [], // 初始化为空数组，符合CharacterCG[]类型
                isPresent: true // 初始时设置为在场
            };
        }) : [];
        // 从蓝图中获取初始场景信息
        const initialLocation = ((_e = (_d = (_c = blueprint === null || blueprint === void 0 ? void 0 : blueprint.worldBuilding) === null || _c === void 0 ? void 0 : _c.locations) === null || _d === void 0 ? void 0 : _d[0]) === null || _e === void 0 ? void 0 : _e.name) || '未知地点';
        const initialEnvironment = ((_h = (_g = (_f = blueprint === null || blueprint === void 0 ? void 0 : blueprint.worldBuilding) === null || _f === void 0 ? void 0 : _f.locations) === null || _g === void 0 ? void 0 : _g[0]) === null || _h === void 0 ? void 0 : _h.description) || '神秘的环境';
        return {
            meta: {
                gameId,
                sessionId,
                gameName,
                playerName,
                saveName: saveName || `${gameName} - ${playerName}开始新冒险`,
                createdAt: new Date().toISOString(),
                lastSavedAt: new Date().toISOString(),
                status: 'active'
            },
            novel: {
                plotSummary: ((_j = blueprint === null || blueprint === void 0 ? void 0 : blueprint.overview) === null || _j === void 0 ? void 0 : _j.description) || '故事开端...',
                currentChapter: 'chapter-1',
                currentSection: 'scene_initial',
                chapterlist: [{ id: 'chapter-1', chapterSummary: '第一章' }],
                events: [],
                // 添加场景信息
                location: initialLocation,
                time: '清晨',
                environment: initialEnvironment
            },
            characters: characters,
            newMessage: [], // 角色属性变化提醒，初始为空
            chatLog: {}
        };
    });
}
// 2. AI 生成初始剧情/角色/事件并应用到 GameState
function generateAndApplyInitialAIData(initialGameState, blueprint, gameId, sessionId, playerName) {
    return __awaiter(this, void 0, void 0, function* () {
        let initialSectionTitle = '初始场景';
        let actionOptions = ["探索周围环境", "查看角色信息", "开始一段对话"];
        let initialGameData = null;
        console.log('[GameSessionsController] 开始生成初始游戏数据...');
        try {
            // 调用 AI 服务生成初始游戏数据
            initialGameData = yield aiService.generateInitialGameData(blueprint);
            console.log('[GameSessionsController] AI 成功生成初始数据');
            // 更新游戏状态
            if (initialGameData && initialGameData.scene) {
                console.log('[GameSessionsController] 更新游戏状态中...');
                // 更新小说数据
                initialGameState.novel.plotSummary = initialGameData.scene.narrativeDescription || '故事开端...';
                initialGameState.novel.currentChapter = initialGameData.scene.chapter || 'chapter-1';
                initialGameState.novel.currentSection = initialGameData.scene.id || 'scene_initial';
                // 更新角色在场状态，但保留已转换的属性格式
                if (initialGameData.characters && Array.isArray(initialGameData.characters)) {
                    // 不要直接覆盖角色数据，而是只更新在场状态
                    initialGameState.characters = initialGameState.characters.map((char) => {
                        const aiChar = initialGameData.characters.find((c) => c.id === char.id);
                        if (aiChar) {
                            return Object.assign(Object.assign({}, char), { isPresent: aiChar.isPresent !== undefined ? aiChar.isPresent : char.isPresent });
                        }
                        return char;
                    });
                    console.log('[GameSessionsController] 角色在场状态已更新，属性格式已保留');
                }
                // 更新选项和标题
                if (initialGameData.suggestedOptions && Array.isArray(initialGameData.suggestedOptions)) {
                    actionOptions = initialGameData.suggestedOptions;
                }
                if (initialGameData.sectionTitle) {
                    initialSectionTitle = initialGameData.sectionTitle;
                }
                console.log('[GameSessionsController] 游戏状态更新完成');
            }
            else {
                console.warn('[GameSessionsController] AI 返回的数据不完整，使用默认值');
            }
        }
        catch (error) {
            console.error('[ERROR] 生成初始游戏数据失败:', error);
            console.log('[GameSessionsController] 使用默认游戏数据');
            // 使用默认数据
            initialGameState.novel.plotSummary = `欢迎来到 ${blueprint.overview.worldName}！${blueprint.overview.shortDescription}`;
            // 变量替换函数
            const replaceVariables = (text) => {
                if (!text)
                    return text;
                return text.replace(/\{\{user\}\}/g, playerName);
            };
            // 如果有角色，设置为在场，但保留原有属性
            if (blueprint.characters && Array.isArray(blueprint.characters)) {
                initialGameState.characters = blueprint.characters.map((char) => (Object.assign(Object.assign({}, char), { name: replaceVariables(char.name), description: replaceVariables(char.description), 
                    // 保留原有的attributeCollections，不要覆盖
                    isPresent: true })));
            }
        }
        return {
            updatedGameState: initialGameState,
            actionOptions,
            initialSectionTitle,
            initialGameData
        };
    });
}
// 3. 写入初始小说章节/小节
function writeInitialNovelSection(gameId, sessionId, aiData, initialGameState) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!aiData || !aiData.initialGameData)
            return;
        const { scene } = aiData.initialGameData;
        const timestamp = new Date().toISOString();
        const novelData = yield fileSystemService.readNovel(gameId, sessionId);
        if (novelData && novelData.chapters) {
            const chapter = novelData.chapters.find((c) => c.id === initialGameState.novel.currentChapter);
            if (chapter) {
                chapter.sections.push({
                    id: initialGameState.novel.currentSection,
                    title: scene.name || aiData.initialSectionTitle,
                    text: scene.narrativeDescription,
                    timestamp
                });
                yield fileSystemService.writeNovel(gameId, sessionId, novelData);
            }
        }
    });
}
// 4. 聊天/欢迎消息初始化
function initializeChatAndLog(initialGameState, gameId, sessionId, playerName, actionOptions, initialSectionTitle, blueprint) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        const systemMessageId = `system_${Date.now()}`;
        const timestamp = new Date().toISOString();
        // 获取主角信息
        const protagonist = (_a = initialGameState.characters) === null || _a === void 0 ? void 0 : _a.find((c) => c.role === '主角');
        const protagonistName = (protagonist === null || protagonist === void 0 ? void 0 : protagonist.name) || playerName;
        const worldName = blueprint.overview.worldName;
        let welcomeContent = `欢迎来到${worldName}！`;
        try {
            // 尝试读取小说内容，获取第一小节
            const novelService = yield import('../services/novel.service.js');
            const novelContent = yield novelService.getNovelContent(gameId, sessionId);
            if (novelContent && novelContent.length > 0) {
                const firstChapter = novelContent[0];
                if (firstChapter.sections && firstChapter.sections.length > 0) {
                    const firstSection = firstChapter.sections[0];
                    // 提取第一小节的前80字作为简短的场景描述
                    let sectionPreview = firstSection.text.substring(0, 80) + '...';
                    // 替换模板变量
                    sectionPreview = sectionPreview.replace(/\{\{user\}\}/g, protagonistName);
                    welcomeContent = `欢迎来到${worldName}！\n\n${sectionPreview}\n\n${protagonistName}，你想要做什么？`;
                    // 确保welcomeContent中的所有{{user}}都被替换
                    welcomeContent = welcomeContent.replace(/\{\{user\}\}/g, protagonistName);
                }
                else {
                    // 如果没有小节内容，使用默认欢迎语
                    welcomeContent = `欢迎来到${worldName}！${protagonistName}，你的冒险即将开始，接下来你要怎么做？`;
                }
            }
            else {
                // 如果没有小说内容，使用默认欢迎语
                welcomeContent = `欢迎来到${worldName}！${protagonistName}，你的冒险即将开始，接下来你要怎么做？`;
            }
        }
        catch (error) {
            console.warn('[GameSessionsController] 读取小说内容失败，使用默认欢迎语:', error);
            welcomeContent = `欢迎来到${worldName}！${protagonistName}，你的冒险即将开始，接下来你要怎么做？`;
        }
        // 确保所有内容都替换了{{user}}变量
        const finalWelcomeContent = welcomeContent.replace(/\{\{user\}\}/g, protagonistName);
        const finalOptions = actionOptions.map(option => option.replace(/\{\{user\}\}/g, protagonistName));
        const finalSectionTitle = initialSectionTitle.replace(/\{\{user\}\}/g, protagonistName);
        const welcomeMessage = {
            id: systemMessageId,
            sender: 'system',
            content: finalWelcomeContent,
            answer: finalWelcomeContent,
            timestamp,
            options: finalOptions,
            sectionTitle: finalSectionTitle
        };
        // 聊天文件 - 使用新格式（多通道结构）
        const chat = { system: [welcomeMessage] };
        try {
            yield fs.writeFile(path.join(db.getGameSavesDir(gameId), `chat_${sessionId}.json`), JSON.stringify(chat, null, 2), 'utf-8');
            console.log(`[GameSessionsController] 成功创建初始聊天消息: ${welcomeMessage.content.substring(0, 100)}...`);
        }
        catch (error) {
            console.error('[ERROR] 写入聊天文件失败:', error);
        }
        // 返回欢迎消息供调用者使用
        return welcomeMessage;
        // chatLog/newMessage
        // 更新chatLog（聊天总结）
        if (!initialGameState.chatLog)
            initialGameState.chatLog = {};
        const key = 'system';
        if (key) {
            initialGameState.chatLog[key] = { chatSummary: welcomeMessage.content, lastMessageId: systemMessageId };
        }
        // newMessage只用于角色属性变化提醒，不包含系统消息
        if (!initialGameState.newMessage)
            initialGameState.newMessage = [];
        // 返回欢迎消息
        return welcomeMessage;
    });
}
/**
 * 创建新的游戏会话（使用新的初始化服务）
 */
export function handleNewSession(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, playerName, saveName } = req.body;
            if (!gameId || !playerName) {
                res.status(400).json({ message: 'Missing required fields: gameId, playerName' });
                return;
            }
            const sessionId = uuidv4();
            console.log(`[GameSessionsController] 使用新初始化服务创建会话: ${sessionId} for game: ${gameId}`);
            // 使用新的游戏初始化服务
            const result = yield gameInitializationService.initializeNewGame(gameId, sessionId, playerName);
            if (result.success) {
                console.log(`[GameSessionsController] 游戏初始化成功`);
                res.status(201).json({
                    sessionId: result.sessionId,
                    gameState: result.gameState,
                    formattedUIContent: result.formattedUIContent,
                    suggestedOptions: result.suggestedOptions,
                    usedDefaultData: false
                });
            }
            else {
                console.error(`[GameSessionsController] 游戏初始化失败:`, result.error);
                res.status(500).json({ message: result.error || 'Failed to initialize game' });
            }
        }
        catch (error) {
            console.error('[ERROR] in handleNewSession:', error);
            res.status(500).json({ message: 'Failed to create new game session' });
        }
    });
}
/**
 * 创建新的游戏会话（旧版本 - 已废弃）
 * @deprecated 请使用 handleNewSession，此方法包含大量冗余代码，将在下个版本移除
 *
 * 此方法的问题：
 * 1. 包含大量硬编码的默认数据生成逻辑
 * 2. AI调用失败时直接使用固定的修仙世界数据
 * 3. 没有充分利用game_meta中的世界设定信息
 * 4. Agent调用逻辑分散且不规范
 * 5. 错误处理机制不完善
 */
export function handleNewSession2(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        try {
            const { gameId, playerName, saveName } = req.body;
            if (!gameId || !playerName) {
                res.status(400).json({ message: 'Missing required fields: gameId, playerName' });
                return;
            }
            const sessionId = uuidv4();
            console.log(`[GameSessionsController] Creating new session: ${sessionId} for game: ${gameId}`);
            const blueprint = yield fileSystemService.readBlueprint(gameId);
            if (!blueprint) {
                res.status(404).json({ message: 'Game blueprint not found' });
                return;
            }
            // 查找初始场景
            const initialScene = blueprint.scenes.find((scene) => scene.isInitial) || blueprint.scenes[0];
            if (!initialScene) {
                res.status(500).json({ message: 'No initial scene found in blueprint' });
                return;
            }
            // 1. 创建基础游戏状态
            const gameName = ((_a = blueprint.overview) === null || _a === void 0 ? void 0 : _a.worldName) || '未知游戏';
            const initialGameState = yield createBaseGameState(gameId, sessionId, playerName, gameName, saveName);
            // 2. 添加角色数据（主角默认在场，其他角色根据角色设定）
            if (blueprint.characters && Array.isArray(blueprint.characters)) {
                console.log(`[GameSessionsController] 开始转换 ${blueprint.characters.length} 个角色的属性格式`);
                // 变量替换函数
                const replaceVariables = (text) => {
                    if (!text)
                        return text;
                    return text.replace(/\{\{user\}\}/g, playerName);
                };
                // 获取 characterAttributeSchema 用于生成标准属性结构
                const characterAttributeSchema = blueprint.characterAttributeSchema;
                console.log(`[GameSessionsController] characterAttributeSchema 可用:`, !!characterAttributeSchema);
                if (characterAttributeSchema && characterAttributeSchema.groups) {
                    console.log(`[GameSessionsController] 属性架构包含 ${characterAttributeSchema.groups.length} 个属性组`);
                }
                initialGameState.characters = blueprint.characters.map((char) => {
                    console.log(`[GameSessionsController] 转换角色: ${char.name} (${char.id})`);
                    console.log(`[GameSessionsController] 原始attributeCollections:`, JSON.stringify(char.attributeCollections, null, 2));
                    console.log(`[GameSessionsController] 原始attributes:`, JSON.stringify(char.attributes, null, 2));
                    let attributeCollections = [];
                    // 优先使用 characterAttributeSchema 生成标准属性结构
                    if (characterAttributeSchema && characterAttributeSchema.groups) {
                        console.log(`[GameSessionsController] 使用 characterAttributeSchema 为角色 ${char.name} 生成标准属性结构`);
                        attributeCollections = characterAttributeSchema.groups.map((group) => ({
                            id: group.id,
                            label: group.name,
                            fields: (group.fields || []).map((field) => {
                                // 尝试从角色的现有属性中获取值
                                let value = getDefaultValueForField(field);
                                let reason = '基于角色设定的初始值';
                                // 如果角色有旧的 attributes 格式，尝试迁移
                                if (char.attributes && char.attributes[field.id] !== undefined) {
                                    value = char.attributes[field.id];
                                    reason = '从角色蓝图迁移';
                                }
                                // 如果角色有 attributeCollections，尝试从中获取
                                else if (char.attributeCollections) {
                                    for (const collection of char.attributeCollections) {
                                        if (collection.fields) {
                                            const existingField = collection.fields.find((f) => f.id === field.id);
                                            if (existingField) {
                                                value = existingField.value;
                                                reason = '从角色蓝图迁移';
                                                break;
                                            }
                                        }
                                    }
                                }
                                return {
                                    id: field.id,
                                    label: field.name,
                                    type: field.displayType,
                                    value: value,
                                    reason: reason
                                };
                            })
                        }));
                        console.log(`[GameSessionsController] 角色 ${char.name} 使用标准属性结构，包含 ${attributeCollections.length} 个属性组`);
                    }
                    else {
                        // 回退方案：转换现有的 attributeCollections
                        console.log(`[GameSessionsController] characterAttributeSchema 不可用，回退到转换现有属性结构`);
                        attributeCollections = (char.attributeCollections || []).map((collection, collectionIndex) => {
                            console.log(`[GameSessionsController] 转换属性集合 ${collectionIndex}:`, collection);
                            const convertedCollection = {
                                id: collection.id || `collection_${collectionIndex}`,
                                label: collection.label || collection.name || `属性组${collectionIndex + 1}`,
                                fields: (collection.attributes || collection.fields || []).map((attr, fieldIndex) => ({
                                    id: attr.id || `field_${fieldIndex}`,
                                    label: attr.label || attr.name || `属性${fieldIndex + 1}`,
                                    type: attr.type || 'text',
                                    value: attr.value || '',
                                    reason: attr.reason || attr.description || '初始设置'
                                }))
                            };
                            console.log(`[GameSessionsController] 转换后的属性集合:`, convertedCollection);
                            return convertedCollection;
                        });
                    }
                    console.log(`[GameSessionsController] 角色 ${char.name} 最终attributeCollections:`, JSON.stringify(attributeCollections, null, 2));
                    // 主角默认在场，其他角色根据蓝图中的isPresent设定
                    const isPresent = char.role === '主角' || char.isPresent === true;
                    const finalCharacter = Object.assign(Object.assign({}, char), { name: replaceVariables(char.name), description: replaceVariables(char.description), // 替换描述中的变量
                        isPresent, // 主角在场，其他角色根据蓝图设定
                        attributeCollections, avatarUrl: char.avatarUrl || null, portraitUrl: char.portraitUrl || null, cg: char.cg || [], characterType: char.characterType || (char.role === '主角' ? 'player' : 'npc') // 添加缺失的characterType字段
                     });
                    console.log(`[GameSessionsController] 角色 ${char.name} 最终数据:`, JSON.stringify(finalCharacter, null, 2));
                    return finalCharacter;
                });
                // 写入角色文件
                yield fileSystemService.writeCharacters(gameId, sessionId, initialGameState.characters);
                console.log(`[GameSessionsController] 初始化角色完成，主角在场: ${(_b = initialGameState.characters.find((c) => c.role === '主角')) === null || _b === void 0 ? void 0 : _b.name}`);
            }
            // 3. 使用AI生成初始游戏数据
            let usedDefaultData = false;
            const aiData = yield generateAndApplyInitialAIData(initialGameState, blueprint, gameId, sessionId, playerName);
            if (aiData && aiData.initialGameData && aiData.initialGameData.scene) {
                // AI生成成功，使用AI生成的数据
                initialGameState.novel.plotSummary = aiData.initialGameData.scene.narrativeDescription || initialGameState.novel.plotSummary;
                // 环境信息已经在novel字段中处理，不需要额外的world字段
                // 3. 写入初始小说章节/小节
                try {
                    // 确保使用正确的章节ID格式
                    const chapterDisplayName = aiData.initialGameData.scene.chapter || "第一章";
                    const chapterId = chapterDisplayName.startsWith("第") ?
                        `chapter-${((_c = chapterDisplayName.match(/\d+/)) === null || _c === void 0 ? void 0 : _c[0]) || "1"}` :
                        chapterDisplayName.replace(/\s+/g, '-').toLowerCase();
                    const sectionId = `${chapterId}-1`;
                    // 更新session数据中的引用
                    initialGameState.novel.currentChapter = chapterId;
                    initialGameState.novel.currentSection = sectionId;
                    // 初始化小说结构
                    const novelData = {
                        chapters: [
                            {
                                id: chapterId,
                                chapter: chapterId,
                                chapter_title: chapterDisplayName,
                                pre_summary: "故事开始",
                                post_summary: aiData.initialSectionTitle || "初始场景",
                                characters: ((_d = initialGameState.characters) === null || _d === void 0 ? void 0 : _d.map(c => c.id)) || [],
                                sections: [
                                    {
                                        id: sectionId,
                                        title: aiData.initialSectionTitle || "初始场景",
                                        text: aiData.initialGameData.scene.narrativeDescription || "你的冒险开始了...",
                                        timestamp: new Date().toISOString()
                                    }
                                ],
                                backgroundImageUrl: "",
                                visualPrompt: {
                                    description: aiData.initialGameData.scene.visualPrompt || "",
                                    style: "default"
                                },
                                time: aiData.initialGameData.scene.time || "",
                                location: aiData.initialGameData.scene.location || "",
                                environment: aiData.initialGameData.scene.environment || ""
                            }
                        ]
                    };
                    // 写入小说文件
                    yield fs.writeFile(path.join(db.getGameSavesDir(gameId), `novel_${sessionId}.json`), JSON.stringify(novelData, null, 2), 'utf-8');
                    console.log(`[GameSessionsController] 成功创建初始小说结构`);
                    // 4. 调用Agent C分析角色在场状态
                    console.log('[GameSessionsController] 准备调用Agent C分析角色在场状态...');
                    try {
                        // 构建Agent A的输出格式，供Agent C分析
                        const agentAOutput = {
                            narrative: aiData.initialGameData.scene.narrativeDescription,
                            chaptersInfo: {
                                id: chapterId,
                                chapterTitle: chapterDisplayName,
                                time: aiData.initialGameData.scene.time || '未知时间',
                                location: aiData.initialGameData.scene.location || '未知地点',
                                environment: aiData.initialGameData.scene.environment || '未知环境'
                            },
                            sectionTitle: aiData.initialSectionTitle || '初始场景',
                            suggestedOptions: aiData.actionOptions || []
                        };
                        console.log('[GameSessionsController] 调用Agent C，输入数据:', {
                            agentAOutput: JSON.stringify(agentAOutput),
                            charactersCount: ((_e = initialGameState.characters) === null || _e === void 0 ? void 0 : _e.length) || 0,
                            userInput: '初始化场景'
                        });
                        const agentCResult = yield aiService.executeAgentCAnalysis(initialGameState, JSON.stringify(agentAOutput), '初始化场景');
                        console.log('[GameSessionsController] Agent C返回结果:', JSON.stringify(agentCResult, null, 2));
                        if (agentCResult && agentCResult.updatedCharacters) {
                            // Agent C 返回的是部分更新数据，需要合并到完整角色对象中
                            console.log('[GameSessionsController] Agent C成功分析角色在场状态');
                            // 合并 Agent C 的更新到现有角色数据
                            agentCResult.updatedCharacters.forEach((update) => {
                                const existingCharIndex = initialGameState.characters.findIndex((char) => char.id === update.id);
                                if (existingCharIndex !== -1) {
                                    // 更新现有角色
                                    if (update.isPresent !== undefined) {
                                        initialGameState.characters[existingCharIndex].isPresent = update.isPresent;
                                    }
                                    if (update.attributeCollections) {
                                        initialGameState.characters[existingCharIndex].attributeCollections = update.attributeCollections;
                                    }
                                    // 不覆盖 name, characterType, description 等基本字段
                                }
                                else {
                                    // 添加新角色 - 检查是否包含必要的字段
                                    if (update.name && update.characterType && update.description) {
                                        console.log(`[GameSessionsController] 添加新角色: ${update.name} (${update.id})`);
                                        initialGameState.characters.push(update);
                                    }
                                    else {
                                        console.warn(`[GameSessionsController] 跳过不完整的新角色: ${update.id}`, update);
                                    }
                                }
                            });
                            // 打印角色在场状态用于调试
                            const presentCharacters = initialGameState.characters.filter((char) => char.isPresent);
                            console.log(`[GameSessionsController] Agent C分析结果：${presentCharacters.length}个角色在场：${presentCharacters.map((char) => char.name || '未知').join(', ')}`);
                            // 重新保存更新后的角色数据到Characters文件
                            yield fileSystemService.writeCharacters(gameId, sessionId, initialGameState.characters);
                            console.log('[GameSessionsController] 已重新保存更新后的角色数据');
                        }
                        else {
                            console.warn('[GameSessionsController] Agent C返回的数据不完整，使用默认角色状态');
                            console.warn('[GameSessionsController] Agent C结果:', JSON.stringify(agentCResult, null, 2));
                            // 如果Agent C分析失败，至少确保主角在场
                            if (initialGameState.characters && initialGameState.characters.length > 0) {
                                // 设置所有角色在场（初始场景通常所有角色都在场）
                                initialGameState.characters.forEach((char, index) => {
                                    char.isPresent = true;
                                    console.log(`[GameSessionsController] 设置角色 ${char.name || char.id} 在场状态为 true`);
                                });
                                // 保存默认状态
                                yield fileSystemService.writeCharacters(gameId, sessionId, initialGameState.characters);
                                console.log('[GameSessionsController] 已保存默认角色状态');
                            }
                        }
                    }
                    catch (error) {
                        console.error('[GameSessionsController] Agent C分析失败，使用默认角色状态:', error);
                        // 如果Agent C分析失败，至少确保主角在场
                        if (initialGameState.characters && initialGameState.characters.length > 0) {
                            // 设置所有角色在场（初始场景通常所有角色都在场）
                            initialGameState.characters.forEach((char, index) => {
                                char.isPresent = true;
                                console.log(`[GameSessionsController] 异常处理：设置角色 ${char.name || char.id} 在场状态为 true`);
                            });
                            // 保存默认状态
                            yield fileSystemService.writeCharacters(gameId, sessionId, initialGameState.characters);
                            console.log('[GameSessionsController] 已保存默认角色状态（异常处理）');
                        }
                    }
                    // 5. 聊天/欢迎消息初始化
                    const welcomeMessage = yield initializeChatAndLog(initialGameState, gameId, sessionId, playerName, aiData.actionOptions || ["探索周围环境", "查看角色信息", "开始一段对话"], aiData.initialSectionTitle || "初始场景", blueprint);
                    // 6. 最终写入 GameState
                    yield fileSystemService.writeSession(gameId, sessionId, initialGameState);
                    // 7. 构建欢迎消息作为 formattedUIContent
                    const formattedUIContent = [welcomeMessage];
                    // 8. 返回响应，包含 formattedUIContent
                    res.status(201).json({
                        sessionId,
                        gameState: initialGameState,
                        formattedUIContent: formattedUIContent || [welcomeMessage],
                        suggestedOptions: aiData.actionOptions || ["探索周围环境", "查看角色信息", "开始一段对话"],
                        usedDefaultData
                    });
                    return;
                }
                catch (error) {
                    console.error('[ERROR] 写入初始小说章节/小节失败:', error);
                }
            }
            else {
                console.warn('[GameSessionsController] AI 返回的数据不完整或为空，使用默认数据');
            }
            // AI生成失败或数据不完整，使用默认数据
            console.log('[GameSessionsController] 使用默认数据创建初始内容');
            // 3. 写入初始小说章节/小节
            try {
                // 确保使用正确的章节ID格式
                const chapterDisplayName = "第一章";
                const chapterId = "chapter-1";
                const sectionId = "chapter-1-1";
                // 更新session数据中的引用
                initialGameState.novel.currentChapter = chapterId;
                initialGameState.novel.currentSection = sectionId;
                // 根据游戏蓝图生成初始小说内容
                const worldName = ((_f = blueprint === null || blueprint === void 0 ? void 0 : blueprint.overview) === null || _f === void 0 ? void 0 : _f.worldName) || '修仙世界';
                const protagonist = (_g = initialGameState.characters) === null || _g === void 0 ? void 0 : _g.find((c) => c.role === '主角');
                const protagonistName = (protagonist === null || protagonist === void 0 ? void 0 : protagonist.name) || '韩立';
                // 初始化小说结构
                const novelData = {
                    chapters: [
                        {
                            id: chapterId,
                            chapter: chapterId,
                            chapter_title: chapterDisplayName,
                            pre_summary: `${protagonistName}踏上修仙之路的开始`,
                            post_summary: `${protagonistName}初入七玄门，开始了修仙生涯`,
                            characters: ((_h = initialGameState.characters) === null || _h === void 0 ? void 0 : _h.map(c => c.id)) || [],
                            sections: [
                                {
                                    id: sectionId,
                                    title: "初入七玄门",
                                    text: `${protagonistName}站在七玄门的山门前，这是一座位于偏远山区的江湖小门派。虽然门派规模不大，但在当地也算小有名气。山门是一座古朴的石牌坊，上书"七玄门"三个大字，字迹苍劲有力。\n\n作为一个来自青牛镇的普通山村少年，${protagonistName}怀着忐忑不安的心情踏进了这个将要改变他一生的地方。虽然资质平庸，只有四灵根，但他心中燃烧着对修仙的渴望。\n\n门派内弟子来往，有的在练习基础拳法，有的在整理药草。空气中弥漫着淡淡的药香和灵气，让人精神为之一振。`,
                                    timestamp: new Date().toISOString()
                                }
                            ],
                            backgroundImageUrl: "",
                            visualPrompt: {
                                description: "古朴的山门，青石铺就的道路，远山如黛，云雾缭绕",
                                style: "古风修仙"
                            },
                            time: "上午",
                            location: "七玄门山门",
                            environment: "山门前，药香阵阵，灵气充沛"
                        }
                    ]
                };
                // 写入小说文件
                yield fs.writeFile(path.join(db.getGameSavesDir(gameId), `novel_${sessionId}.json`), JSON.stringify(novelData, null, 2), 'utf-8');
                console.log(`[GameSessionsController] 成功创建初始小说结构（使用默认数据）`);
            }
            catch (error) {
                console.error('[ERROR] 写入初始小说章节/小节失败:', error);
            }
            // 4. 聊天/欢迎消息初始化
            const protagonist = (_j = initialGameState.characters) === null || _j === void 0 ? void 0 : _j.find((c) => c.role === '主角');
            const protagonistName = (protagonist === null || protagonist === void 0 ? void 0 : protagonist.name) || '韩立';
            const actionOptions = [
                "在七玄门内四处走走，熟悉环境",
                "寻找其他弟子交流",
                "前往药园看看有什么任务"
            ];
            const welcomeMessage = yield initializeChatAndLog(initialGameState, gameId, sessionId, playerName, actionOptions, "初入七玄门", blueprint);
            // 5. 最终写入 GameState
            yield fileSystemService.writeSession(gameId, sessionId, initialGameState);
            // 6. 构建欢迎消息作为 formattedUIContent
            const formattedUIContent = [welcomeMessage];
            // 7. 返回响应，包含 formattedUIContent
            res.status(201).json({
                sessionId,
                gameState: initialGameState,
                formattedUIContent: formattedUIContent || [welcomeMessage],
                suggestedOptions: actionOptions,
                usedDefaultData: true
            });
            return;
        }
        catch (error) {
            console.error('[ERROR] in handleNewSession2:', error);
            res.status(500).json({ message: 'Failed to create new game session' });
        }
    });
}
/**
 * 处理玩家交互请求（导演模式或参与者模式）
 */
export function interact(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c;
        try {
            console.log(`[GameSessionController] 开始处理交互请求: ${req.method} ${req.originalUrl}`);
            console.log(`[GameSessionController] 请求参数: gameId=${req.params.gameId}, sessionId=${req.params.sessionId}`);
            console.log(`[GameSessionController] 请求体: ${JSON.stringify(req.body)}`);
            const { gameId, sessionId } = req.params;
            const { prompt, characterId } = req.body;
            // 验证输入
            if (!prompt || typeof prompt !== 'string') {
                console.error(`[GameSessionController] 缺少有效的提示词: ${prompt}`);
                res.status(400).json({ error: '缺少有效的提示词' });
                return;
            }
            console.log(`[GameSessionController] 提示词: "${prompt}", 角色ID: ${characterId || '无(导演模式)'}`);
            // 读取当前游戏状态
            console.log(`[GameSessionController] 读取游戏状态: gameId=${gameId}, sessionId=${sessionId}`);
            const gameState = yield fileSystemService.readSession(gameId, sessionId);
            console.log(`[GameSessionController] 游戏状态读取成功, 角色数量: ${((_a = gameState.characters) === null || _a === void 0 ? void 0 : _a.length) || 0}`);
            // 根据是否提供characterId决定使用哪种模式
            let response;
            if (characterId) {
                // 参与者模式 - 与特定角色对话
                console.log(`[GameSessionController] 执行参与者模式流程，与角色 ${characterId} 对话`);
                response = yield aiService.executeParticipantFlow(gameState, { prompt, characterId });
                console.log(`[GameSessionController] 参与者模式流程执行完成，生成了 ${((_b = response.formattedUIContent) === null || _b === void 0 ? void 0 : _b.length) || 0} 条消息`);
            }
            else {
                // 导演模式 - 宏观指导故事发展
                console.log('[GameSessionController] 执行导演模式流程');
                response = yield aiService.executeDirectorFlow(gameState, { prompt });
                console.log(`[GameSessionController] 导演模式流程执行完成，生成了 ${((_c = response.formattedUIContent) === null || _c === void 0 ? void 0 : _c.length) || 0} 条消息`);
            }
            // 保存更新后的游戏状态
            console.log(`[GameSessionController] 保存更新后的游戏状态`);
            yield fileSystemService.writeSession(gameId, sessionId, response.updatedGameState);
            console.log(`[GameSessionController] 游戏状态保存成功`);
            // 注意：chat消息的保存已经在AI服务中处理，这里不需要重复保存
            console.log(`[GameSessionController] Chat消息已在AI服务中保存，跳过重复保存`);
            console.log(`[GameSessionController] 系统/AI回复添加成功`);
            // 确保 novel 结构被正确保存
            if (response.updatedGameState.novel && response.updatedGameState.novel.chapters) {
                const novelChapters = response.updatedGameState.novel.chapters;
                if (Array.isArray(novelChapters)) {
                    console.log(`[GameSessionController] 保存小说内容，章节数: ${novelChapters.length}`);
                    yield novelService.saveNovelContent(gameId, sessionId, novelChapters);
                    console.log('[GameSessionController] 小说内容保存成功');
                }
            }
            // 读取更新后的游戏状态
            const updatedGameState = yield fileSystemService.readSession(gameId, sessionId);
            // 构建响应 - 包含完整的游戏状态
            const responseData = {
                success: true,
                updatedGameState: updatedGameState, // 添加完整的游戏状态
                formattedUIContent: response.formattedUIContent || [],
                messages: response.formattedUIContent || [], // 保持向后兼容
                suggestedOptions: response.suggestedOptions || [],
                sectionTitle: response.sectionTitle || '',
                answer: response.answer
            };
            console.log(`[GameSessionController] 发送响应: ${JSON.stringify({
                success: responseData.success,
                messageCount: responseData.messages.length,
                optionCount: responseData.suggestedOptions.length
            })}`);
            res.status(200).json(responseData);
            console.log(`[GameSessionController] 交互请求处理完成`);
        }
        catch (error) {
            console.error('[ERROR] 处理交互请求失败:', error);
            res.status(500).json({ error: '处理交互请求失败', details: error.message });
        }
    });
}
/**
 * 删除特定游戏存档
 */
export function handleDeleteSession(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        const { gameId, sessionId } = req.params;
        try {
            console.log(`[API] DELETE /api/game/${gameId}/session/${sessionId} called`);
            const savesDir = db.getGameSavesDir(gameId);
            // 1. 读取 saves 目录下所有文件
            const files = yield fs.readdir(savesDir);
            // 2. 找出所有包含 sessionId 的 json 文件
            const sessionFiles = files.filter(f => f.endsWith('.json') && f.includes(sessionId));
            if (sessionFiles.length === 0) {
                res.status(404).json({ message: '未找到该会话的任何存档文件' });
                return;
            }
            // 3. 依次删除
            for (const file of sessionFiles) {
                const filePath = path.join(savesDir, file);
                try {
                    yield fs.unlink(filePath);
                    console.log(`[INFO] Deleted: ${filePath}`);
                }
                catch (err) {
                    console.error(`[ERROR] Failed to delete: ${filePath}`, err);
                }
            }
            res.status(200).json({ message: '该会话的所有存档文件已删除', deletedSessionId: sessionId, deletedFiles: sessionFiles });
        }
        catch (error) {
            console.error('[ERROR] in handleDeleteSession:', error);
            res.status(500).json({ message: '删除游戏存档失败，请稍后重试' });
        }
    });
}
/**
 * AI重写小说小节
 * POST /api/game/:gameId/session/:sessionId/novel/rewrite-section
 * body: { chapterId, sectionId, prompt }
 */
export function handleRewriteNovelSection(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId } = req.params;
            const { chapterId, sectionId, prompt } = req.body;
            if (!chapterId || !sectionId || !prompt) {
                res.status(400).json({ message: 'Missing required fields' });
                return;
            }
            // 1. 读取当前小节内容
            const savesDir = db.getGameSavesDir(gameId);
            const novelPath = path.join(savesDir, `novel_${sessionId}.json`);
            const novelRaw = yield fs.readFile(novelPath, 'utf-8');
            const novel = JSON.parse(novelRaw);
            const chapter = novel.chapters.find((c) => c.id === chapterId);
            if (!chapter)
                throw new Error('Chapter not found');
            const oldSection = chapter.sections.find((s) => s.id === sectionId);
            if (!oldSection)
                throw new Error('Section not found');
            // 2. AI生成新内容
            const newSection = yield aiService.rewriteNovelSection(oldSection, prompt);
            // 3. 替换内容库
            yield fileSystemService.replaceNovelSection(gameId, sessionId, { chapterId, sectionId, newSection });
            // 4. 同步更新gamestate引用
            const gameState = yield fileSystemService.readSession(gameId, sessionId);
            // 更新novel结构
            if (!gameState.novel) {
                gameState.novel = {
                    plotSummary: '',
                    currentChapter: chapterId,
                    currentSection: newSection.id,
                    chapterlist: [],
                    events: []
                };
            }
            else {
                gameState.novel.currentChapter = chapterId;
                gameState.novel.currentSection = newSection.id;
            }
            // 更新chapterlist
            const chapterSummaryIndex = gameState.novel.chapterlist.findIndex(cs => cs.id === chapterId);
            if (chapterSummaryIndex === -1) {
                // 如果章节摘要不存在，创建新的
                gameState.novel.chapterlist.push({
                    id: chapterId,
                    chapterSummary: chapter.title || `第${gameState.novel.chapterlist.length + 1}章`
                });
            }
            yield fileSystemService.writeSession(gameId, sessionId, gameState);
            res.status(200).json({ gameState });
        }
        catch (error) {
            console.error('[ERROR] in handleRewriteNovelSection:', error);
            res.status(500).json({ message: 'Failed to rewrite novel section' });
        }
    });
}
/**
 * AI重写对话消息
 * POST /api/game/:gameId/session/:sessionId/chat/rewrite-message
 * body: { messageId, prompt }
 */
export function handleRewriteChatMessage(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId } = req.params;
            const { messageId, prompt } = req.body;
            if (!messageId || !prompt) {
                res.status(400).json({ message: 'Missing required fields' });
                return;
            }
            // 1. 读取当前消息内容
            const chatData = yield fileSystemService.readChatData(gameId, sessionId);
            // 2. 查找消息所在的通道和消息对象
            let oldMessage = null;
            let channelKey = '';
            // 先检查是否在system通道
            if (chatData.system && Array.isArray(chatData.system)) {
                oldMessage = chatData.system.find((m) => m.id === messageId);
                if (oldMessage) {
                    channelKey = 'system';
                }
            }
            // 如果不在system通道，检查其他通道
            if (!oldMessage) {
                for (const key of Object.keys(chatData)) {
                    if (key !== 'system' && Array.isArray(chatData[key])) {
                        oldMessage = chatData[key].find((m) => m.id === messageId);
                        if (oldMessage) {
                            channelKey = key;
                            break;
                        }
                    }
                }
            }
            if (!oldMessage)
                throw new Error('Message not found in any channel');
            // 2. AI生成新内容
            const newMessage = yield aiService.rewriteChatMessage(oldMessage, prompt);
            // 3. 替换内容库
            yield fileSystemService.replaceChatMessage(gameId, sessionId, { messageId, newMessage });
            // 4. 同步更新gamestate引用
            const gameState = yield fileSystemService.readSession(gameId, sessionId);
            // interactionLog已废弃，消息现在存储在聊天文件中
            // 5. 更新聊天引用
            if (!gameState.chatLog) {
                gameState.chatLog = {};
            }
            // 根据消息ID提取键
            const key = messageId.split('_')[0];
            if (key) {
                // 更新chatLog
                gameState.chatLog[key] = {
                    chatSummary: newMessage.answer || newMessage.content,
                    lastMessageId: messageId
                };
                // newMessage只用于角色属性变化提醒，聊天消息重写不应该更新它
                // 角色属性变化应该由Agent C在处理交互时更新
                //
                // 示例：如何正确添加角色属性变化
                // addCharacterAttributeChange(gameState, 'char_1', '得到一件法宝，灵力增长200');
            }
            yield fileSystemService.writeSession(gameId, sessionId, gameState);
            res.status(200).json({ gameState });
        }
        catch (error) {
            console.error('[ERROR] in handleRewriteChatMessage:', error);
            res.status(500).json({ message: 'Failed to rewrite chat message' });
        }
    });
}
/**
 * 更新当前场景引用
 */
export function handleUpdateScene(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId } = req.params;
            const { sceneId, sceneName, chapterId, chapterTitle } = req.body;
            if (!sceneId || !sceneName || !chapterId) {
                res.status(400).json({ error: '缺少必要参数' });
                return;
            }
            console.log(`[GameSessionsController] 更新当前场景引用: ${gameId}/${sessionId}`);
            console.log(`[GameSessionsController] 场景ID: ${sceneId}, 场景名称: ${sceneName}, 章节ID: ${chapterId}, 章节标题: ${chapterTitle || '未提供'}`);
            // 1. 读取当前游戏状态
            const sessionPath = path.join(db.getGameSavesDir(gameId), `session_${sessionId}.json`);
            const sessionData = yield fs.readFile(sessionPath, 'utf-8');
            const session = JSON.parse(sessionData);
            // 2. 更新当前场景引用
            if (!session.novel) {
                session.novel = {
                    plotSummary: '',
                    currentChapter: chapterId,
                    currentSection: sceneId,
                    chapterlist: [{
                            id: chapterId,
                            chapterSummary: chapterTitle || `章节 ${chapterId}`
                        }],
                    events: []
                };
            }
            else {
                session.novel.currentChapter = chapterId;
                session.novel.currentSection = sceneId;
                // 更新或添加章节摘要
                const chapterIdx = session.novel.chapterlist.findIndex(c => c.id === chapterId);
                if (chapterIdx !== -1) {
                    if (chapterTitle) {
                        session.novel.chapterlist[chapterIdx].chapterSummary = chapterTitle;
                    }
                }
                else {
                    session.novel.chapterlist.push({
                        id: chapterId,
                        chapterSummary: chapterTitle || `章节 ${chapterId}`
                    });
                }
            }
            // 添加事件记录
            const newEvent = {
                id: `event_${Date.now()}`,
                description: `场景切换到: ${sceneName}`,
                timestamp: new Date().toISOString()
            };
            if (!session.novel.events) {
                session.novel.events = [newEvent];
            }
            else {
                // 保持事件数组长度在合理范围内
                if (session.novel.events.length >= 10) {
                    session.novel.events = session.novel.events.slice(-9);
                }
                session.novel.events.push(newEvent);
            }
            // 3. 保存更新后的游戏状态
            yield fs.writeFile(sessionPath, JSON.stringify(session, null, 2), 'utf-8');
            console.log(`[GameSessionsController] 成功更新当前场景引用: chapterId=${chapterId}, sectionId=${sceneId}`);
            // 4. 返回成功响应
            res.status(200).json({ success: true });
        }
        catch (error) {
            console.error(`[ERROR] 更新当前场景引用失败:`, error);
            res.status(500).json({ error: '更新当前场景引用失败' });
        }
    });
}
/**
 * 获取指定对象的聊天消息
 */
export function handleGetChatMessages(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId, targetId } = req.params;
            console.log(`[GameSessionsController] 获取${targetId}的聊天消息`);
            // 读取聊天文件
            try {
                const chatData = yield fileSystemService.readChatData(gameId, sessionId);
                let filteredMessages = [];
                if (targetId === 'ai') { // Director Mode
                    if (chatData.system && Array.isArray(chatData.system)) {
                        filteredMessages = chatData.system.filter((msg) => {
                            // Include system/ai messages without a characterId (general director responses)
                            // Include user messages (these are director prompts)
                            return ((msg.sender === 'system' || msg.sender === 'ai') && !msg.characterId) || msg.sender === 'user';
                        });
                    }
                }
                else if (chatData[`char_${targetId}`]) { // Character-specific chat
                    const characterChannel = `char_${targetId}`;
                    if (chatData[characterChannel] && Array.isArray(chatData[characterChannel])) {
                        filteredMessages = chatData[characterChannel];
                    }
                    // Optionally, include system messages DIRECTLY related to this character if your logic requires.
                    // For now, keeping character channels separate from general system/director messages.
                    // if (chatData.system && Array.isArray(chatData.system)) {
                    //   const relatedSystemMessages = chatData.system.filter((msg: any) => msg.characterId === targetId);
                    //   filteredMessages = [...filteredMessages, ...relatedSystemMessages];
                    // }
                }
                else if (targetId === 'system') { // Specific request for 'system' channel raw (includes all user inputs and general system/AI)
                    if (chatData.system && Array.isArray(chatData.system)) {
                        filteredMessages = chatData.system;
                    }
                }
                else {
                    // 对于其他targetId（比如角色ID），如果没有专门的字段，则从system字段中查找相关消息
                    // 这种情况通常发生在聊天模式下，消息存储在system字段中
                    if (chatData.system && Array.isArray(chatData.system)) {
                        // 返回所有system消息，因为聊天模式的消息都存储在这里
                        filteredMessages = chatData.system;
                    }
                }
                // Fallback for other targetIds or if specific channel is empty but system messages might be relevant
                // This part might need refinement based on how you want to handle unknown targetIds or empty char channels.
                // For now, if not 'ai', not a char channel, and not 'system', it will result in empty or default welcome.
                // Sort all collected messages by timestamp
                filteredMessages.sort((a, b) => {
                    const timeA = new Date(a.timestamp).getTime();
                    const timeB = new Date(b.timestamp).getTime();
                    return timeA - timeB;
                });
                console.log(`[GameSessionsController] 找到${filteredMessages.length}条消息 for targetId: ${targetId}`);
                // 保持与前端API一致的返回格式
                res.status(200).json({ messages: filteredMessages });
                return;
            }
            catch (error) {
                console.error(`[ERROR] 读取聊天文件失败:`, error);
            }
            // 如果没有找到消息，并且是系统消息，则创建一个基于第一小节的欢迎消息
            if (targetId === 'system') {
                try {
                    // 读取游戏蓝图和游戏状态
                    const blueprint = yield fileSystemService.readBlueprint(gameId);
                    const gameState = yield fileSystemService.readSession(gameId, sessionId);
                    // 读取小说内容，获取第一小节
                    const novelService = yield import('../services/novel.service.js');
                    const novelContent = yield novelService.getNovelContent(gameId, sessionId);
                    let welcomeContent = `欢迎来到 ${blueprint.overview.worldName}！`;
                    let options = ["探索周围环境", "查看角色信息", "开始一段对话"];
                    let sectionTitle = "初始场景";
                    // 如果有小说内容，使用第一章第一节的信息
                    if (novelContent && novelContent.length > 0) {
                        const firstChapter = novelContent[0];
                        if (firstChapter.sections && firstChapter.sections.length > 0) {
                            const firstSection = firstChapter.sections[0];
                            // 提取第一小节的前80字作为简短的场景描述
                            let sectionPreview = firstSection.text.substring(0, 80) + '...';
                            // 替换模板变量
                            sectionPreview = sectionPreview.replace(/\{\{user\}\}/g, gameState.meta.playerName);
                            welcomeContent = `欢迎来到 ${blueprint.overview.worldName}！\n\n${sectionPreview}\n\n${gameState.meta.playerName}，你想要做什么？`;
                            // 确保welcomeContent中的所有{{user}}都被替换
                            welcomeContent = welcomeContent.replace(/\{\{user\}\}/g, gameState.meta.playerName);
                            sectionTitle = firstChapter.chapter_title || "初始场景";
                            // 尝试从第一小节中提取更相关的选项
                            options = [
                                "继续探索当前场景",
                                "与在场的角色交谈",
                                "查看周围环境",
                                "思考下一步行动"
                            ];
                        }
                    }
                    // 创建欢迎消息，确保所有内容都替换了{{user}}变量
                    const systemMessageId = `system_${Date.now()}`;
                    const finalWelcomeContent = welcomeContent.replace(/\{\{user\}\}/g, gameState.meta.playerName);
                    const finalOptions = options.map(option => option.replace(/\{\{user\}\}/g, gameState.meta.playerName));
                    const finalSectionTitle = sectionTitle.replace(/\{\{user\}\}/g, gameState.meta.playerName);
                    const welcomeMessage = {
                        id: systemMessageId,
                        sender: 'system',
                        content: finalWelcomeContent,
                        answer: finalWelcomeContent,
                        timestamp: new Date().toISOString(),
                        options: finalOptions,
                        sectionTitle: finalSectionTitle
                    };
                    // 保持与前端API一致的返回格式
                    res.status(200).json({ messages: [welcomeMessage] });
                    return;
                }
                catch (error) {
                    console.error('[ERROR] 创建欢迎消息失败:', error);
                    // 降级到简单的欢迎消息
                    const blueprint = yield fileSystemService.readBlueprint(gameId);
                    const gameState = yield fileSystemService.readSession(gameId, sessionId);
                    const systemMessageId = `system_${Date.now()}`;
                    const welcomeMessage = {
                        id: systemMessageId,
                        sender: 'system',
                        content: `欢迎来到 ${blueprint.overview.worldName}！${gameState.meta.playerName}，接下来你要怎么做？`,
                        answer: `欢迎来到 ${blueprint.overview.worldName}！${gameState.meta.playerName}，接下来你要怎么做？`,
                        timestamp: new Date().toISOString(),
                        options: ["探索周围环境", "查看角色信息", "开始一段对话"],
                        sectionTitle: "初始场景"
                    };
                    res.status(200).json({ messages: [welcomeMessage] });
                    return;
                }
            }
            // 如果是角色消息，返回空数组
            res.status(200).json({ messages: [] });
        }
        catch (error) {
            console.error(`[ERROR] 获取聊天消息失败:`, error);
            res.status(500).json({ error: '获取聊天消息失败' });
        }
    });
}
/**
 * 添加角色属性变化到newMessage（用于UI提醒）
 * 这个函数应该由Agent C在检测到角色属性变化时调用
 */
export function addCharacterAttributeChange(gameState, characterId, changeMessage) {
    if (!gameState.newMessage) {
        gameState.newMessage = [];
    }
    const newChange = {
        [characterId]: {
            time: new Date().toISOString(),
            msg: changeMessage // 例如："得到一件法宝，灵力增长200"
        }
    };
    // 查找是否已存在该角色的变化记录
    const existingIdx = gameState.newMessage.findIndex((item) => Object.keys(item)[0] === characterId);
    if (existingIdx !== -1) {
        // 更新现有记录
        gameState.newMessage[existingIdx] = newChange;
    }
    else {
        // 添加新记录，控制数组长度不超过5个
        if (gameState.newMessage.length >= 5) {
            gameState.newMessage.shift();
        }
        gameState.newMessage.push(newChange);
    }
    console.log(`[GameSessions] 添加角色属性变化: ${characterId} - ${changeMessage}`);
}
/**
 * 清除角色属性变化通知
 */
export function clearCharacterNotification(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId } = req.params;
            const { characterId } = req.body;
            console.log(`[API] POST /api/game/${gameId}/session/${sessionId}/clear-notification called for character: ${characterId}`);
            if (!characterId) {
                res.status(400).json({ message: 'Invalid request: missing characterId' });
                return;
            }
            // 读取当前游戏状态
            const gameState = yield fileSystemService.readSession(gameId, sessionId);
            if (!gameState.newMessage) {
                res.status(200).json({ gameState });
                return;
            }
            // 移除指定角色的通知消息
            gameState.newMessage = gameState.newMessage.filter((messageObj) => !messageObj[characterId]);
            // 保存更新后的游戏状态
            yield fileSystemService.writeSession(gameId, sessionId, gameState);
            console.log(`[GameSessions] 已清除角色 ${characterId} 的属性变化通知`);
            res.status(200).json({ gameState });
        }
        catch (error) {
            console.error('[ERROR] in clearCharacterNotification:', error);
            res.status(500).json({ message: 'Failed to clear character notification' });
        }
    });
}
/**
 * 加载游戏 - 前端API兼容
 * POST /api/games/load
 */
export function handleLoadGame(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId } = req.body;
            if (!gameId) {
                res.status(400).json({ message: 'Missing required field: gameId' });
                return;
            }
            console.log(`[API] POST /api/games/load called with gameId=${gameId}, sessionId=${sessionId}`);
            if (sessionId) {
                // 加载特定会话
                const gameState = yield fileSystemService.readSession(gameId, sessionId);
                res.status(200).json(gameState);
            }
            else {
                // 如果没有sessionId，返回游戏蓝图信息
                const blueprint = yield fileSystemService.readBlueprint(gameId);
                res.status(200).json({ blueprint });
            }
        }
        catch (error) {
            console.error('[ERROR] in handleLoadGame:', error);
            res.status(500).json({ message: 'Failed to load game' });
        }
    });
}
/**
 * 游戏交互 - 前端API兼容
 * POST /api/games/interact
 */
export function handleInteract(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { gameId, sessionId, prompt, characterId, type, chapterId } = req.body;
            if (!gameId || !sessionId) {
                res.status(400).json({ message: 'Missing required fields: gameId, sessionId' });
                return;
            }
            console.log(`[API] POST /api/games/interact called with gameId=${gameId}, sessionId=${sessionId}, type=${type}`);
            // 处理特殊交互类型
            if (type === 'switch_chapter') {
                if (!chapterId) {
                    res.status(400).json({ message: 'Missing chapterId for switch_chapter' });
                    return;
                }
                // 处理章节切换
                req.params.gameId = gameId;
                req.params.sessionId = sessionId;
                req.body = { chapterId, chapterTitle: '', sceneId: 'section-1', sceneName: '' };
                yield handleUpdateScene(req, res);
                return;
            }
            // 标准交互处理
            if (!prompt) {
                res.status(400).json({ message: 'Missing required field: prompt' });
                return;
            }
            // 重定向到现有的交互处理逻辑
            req.params.gameId = gameId;
            req.params.sessionId = sessionId;
            req.body = { prompt, characterId };
            yield interact(req, res);
        }
        catch (error) {
            console.error('[ERROR] in handleInteract:', error);
            res.status(500).json({ message: 'Failed to process interaction' });
        }
    });
}
